import 'package:supabase_flutter/supabase_flutter.dart';

import './supabase_service.dart';

class AppointmentService {
  static final SupabaseClient _client = SupabaseService.instance.client;

  // Get today's appointments with client and service details
  static Future<List<Map<String, dynamic>>> getTodaysAppointments() async {
    try {
      final response = await _client
          .from('appointments')
          .select('''
            *,
            clients(full_name, phone_number, address),
            service_types(name, duration_minutes)
          ''')
          .eq('scheduled_date', DateTime.now().toIso8601String().split('T')[0])
          .inFilter('status', ['scheduled', 'confirmed', 'in_progress'])
          .order('scheduled_time');
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch today\'s appointments: $error');
    }
  }

  // Get appointments for a specific date range
  static Future<List<Map<String, dynamic>>> getAppointmentsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final response = await _client
          .from('appointments')
          .select('''
            *,
            clients(full_name, phone_number, address),
            service_types(name, duration_minutes)
          ''')
          .gte('scheduled_date', startDate.toIso8601String().split('T')[0])
          .lte('scheduled_date', endDate.toIso8601String().split('T')[0])
          .order('scheduled_date')
          .order('scheduled_time');
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch appointments: $error');
    }
  }

  // Create new appointment
  static Future<Map<String, dynamic>> createAppointment({
    required String clientId,
    required String serviceTypeId,
    required DateTime scheduledDate,
    String? scheduledTime,
    String? recurringScheduleId,
    String? notes,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final appointmentData = {
        'user_id': userId,
        'client_id': clientId,
        'service_type_id': serviceTypeId,
        'scheduled_date': scheduledDate.toIso8601String().split('T')[0],
        'scheduled_time': scheduledTime,
        'recurring_schedule_id': recurringScheduleId,
        'notes': notes,
        'status': 'scheduled',
      };

      final response =
          await _client.from('appointments').insert(appointmentData).select('''
            *,
            clients(full_name, phone_number, address),
            service_types(name, duration_minutes)
          ''').single();
      return response;
    } catch (error) {
      throw Exception('Failed to create appointment: $error');
    }
  }

  // Update appointment status
  static Future<Map<String, dynamic>> updateAppointmentStatus({
    required String appointmentId,
    required String status,
    String? notes,
  }) async {
    try {
      final updateData = {
        'status': status,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (notes != null) updateData['notes'] = notes;
      if (status == 'completed') {
        updateData['completed_at'] = DateTime.now().toIso8601String();
      }

      final response = await _client
          .from('appointments')
          .update(updateData)
          .eq('id', appointmentId)
          .select('''
            *,
            clients(full_name, phone_number, address),
            service_types(name, duration_minutes)
          ''').single();
      return response;
    } catch (error) {
      throw Exception('Failed to update appointment: $error');
    }
  }

  // Get appointment by ID
  static Future<Map<String, dynamic>> getAppointmentById(
      String appointmentId) async {
    try {
      final response = await _client.from('appointments').select('''
            *,
            clients(full_name, phone_number, address, email),
            service_types(name, duration_minutes, description, price),
            recurring_schedules(recurrence_months, next_due_date)
          ''').eq('id', appointmentId).single();
      return response;
    } catch (error) {
      throw Exception('Failed to fetch appointment: $error');
    }
  }

  // Get upcoming appointments (next 7 days)
  static Future<List<Map<String, dynamic>>> getUpcomingAppointments() async {
    try {
      final today = DateTime.now();
      final nextWeek = today.add(const Duration(days: 7));

      final response = await _client
          .from('appointments')
          .select('''
            *,
            clients(full_name, phone_number),
            service_types(name)
          ''')
          .gte('scheduled_date', today.toIso8601String().split('T')[0])
          .lte('scheduled_date', nextWeek.toIso8601String().split('T')[0])
          .inFilter('status', ['scheduled', 'confirmed'])
          .order('scheduled_date')
          .order('scheduled_time');
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch upcoming appointments: $error');
    }
  }

  // Reschedule appointment
  static Future<Map<String, dynamic>> rescheduleAppointment({
    required String appointmentId,
    required DateTime newDate,
    String? newTime,
    String? notes,
  }) async {
    try {
      final updateData = {
        'scheduled_date': newDate.toIso8601String().split('T')[0],
        'scheduled_time': newTime,
        'status': 'rescheduled',
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (notes != null) updateData['notes'] = notes;

      final response = await _client
          .from('appointments')
          .update(updateData)
          .eq('id', appointmentId)
          .select('''
            *,
            clients(full_name, phone_number, address),
            service_types(name, duration_minutes)
          ''').single();
      return response;
    } catch (error) {
      throw Exception('Failed to reschedule appointment: $error');
    }
  }
}
