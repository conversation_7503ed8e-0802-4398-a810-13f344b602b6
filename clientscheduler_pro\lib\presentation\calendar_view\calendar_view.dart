import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/calendar_grid_widget.dart';
import './widgets/calendar_header_widget.dart';
import './widgets/day_detail_bottom_sheet.dart';
import './widgets/week_view_widget.dart';

class CalendarView extends StatefulWidget {
  const CalendarView({Key? key}) : super(key: key);

  @override
  State<CalendarView> createState() => _CalendarViewState();
}

class _CalendarViewState extends State<CalendarView>
    with TickerProviderStateMixin {
  late DateTime _currentMonth;
  late DateTime _selectedDate;
  late DateTime _today;
  late PageController _pageController;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  // Mock appointment data
  final Map<DateTime, List<Map<String, dynamic>>> _appointments = {};

  @override
  void initState() {
    super.initState();
    _today = DateTime.now();
    _currentMonth = DateTime(_today.year, _today.month, 1);
    _selectedDate = _today;
    _pageController = PageController(initialPage: 1000);

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0),
      end: const Offset(0, 0),
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));

    _initializeMockData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _initializeMockData() {
    final mockAppointments = [
      {
        "id": 1,
        "clientName": "Sarah Johnson",
        "serviceType": "Cleaning",
        "time": "09:00 AM",
        "address": "123 Oak Street, Downtown",
        "phone": "(*************",
        "email": "<EMAIL>",
        "isOverdue": false,
        "date": DateTime.now().add(Duration(days: 1)),
      },
      {
        "id": 2,
        "clientName": "Michael Chen",
        "serviceType": "Maintenance",
        "time": "02:30 PM",
        "address": "456 Pine Avenue, Westside",
        "phone": "(*************",
        "email": "<EMAIL>",
        "isOverdue": false,
        "date": DateTime.now().add(Duration(days: 1)),
      },
      {
        "id": 3,
        "clientName": "Emily Rodriguez",
        "serviceType": "Consultation",
        "time": "11:15 AM",
        "address": "789 Maple Drive, Eastside",
        "phone": "(*************",
        "email": "<EMAIL>",
        "isOverdue": true,
        "date": DateTime.now().subtract(Duration(days: 1)),
      },
      {
        "id": 4,
        "clientName": "David Thompson",
        "serviceType": "Training",
        "time": "04:00 PM",
        "address": "321 Elm Street, Northside",
        "phone": "(*************",
        "email": "<EMAIL>",
        "isOverdue": false,
        "date": DateTime.now(),
      },
      {
        "id": 5,
        "clientName": "Lisa Anderson",
        "serviceType": "Cleaning",
        "time": "10:30 AM",
        "address": "654 Cedar Lane, Southside",
        "phone": "(*************",
        "email": "<EMAIL>",
        "isOverdue": false,
        "date": DateTime.now().add(Duration(days: 3)),
      },
      {
        "id": 6,
        "clientName": "Robert Wilson",
        "serviceType": "Maintenance",
        "time": "01:45 PM",
        "address": "987 Birch Road, Central",
        "phone": "(*************",
        "email": "<EMAIL>",
        "isOverdue": false,
        "date": DateTime.now().add(Duration(days: 5)),
      },
      {
        "id": 7,
        "clientName": "Jennifer Davis",
        "serviceType": "Consultation",
        "time": "03:20 PM",
        "address": "147 Spruce Court, Riverside",
        "phone": "(*************",
        "email": "<EMAIL>",
        "isOverdue": false,
        "date": DateTime.now().add(Duration(days: 7)),
      },
      {
        "id": 8,
        "clientName": "Christopher Lee",
        "serviceType": "Training",
        "time": "08:00 AM",
        "address": "258 Willow Street, Hillside",
        "phone": "(*************",
        "email": "<EMAIL>",
        "isOverdue": false,
        "date": DateTime.now().add(Duration(days: 2)),
      },
    ];

    // Group appointments by date
    for (var appointment in mockAppointments) {
      final date = appointment['date'] as DateTime;
      final dateKey = DateTime(date.year, date.month, date.day);

      if (_appointments[dateKey] == null) {
        _appointments[dateKey] = [];
      }
      _appointments[dateKey]!.add(appointment);
    }

    // Sort appointments by time for each date
    _appointments.forEach((date, appointments) {
      appointments.sort((a, b) {
        final timeA = a['time'] as String;
        final timeB = b['time'] as String;
        return timeA.compareTo(timeB);
      });
    });
  }

  void _onPreviousMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1, 1);
    });
    _slideController.forward().then((_) => _slideController.reset());
  }

  void _onNextMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 1);
    });
    _slideController.forward().then((_) => _slideController.reset());
  }

  void _onTodayPressed() {
    setState(() {
      _currentMonth = DateTime(_today.year, _today.month, 1);
      _selectedDate = _today;
    });
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
      if (date.month != _currentMonth.month) {
        _currentMonth = DateTime(date.year, date.month, 1);
      }
    });

    // Show day detail bottom sheet if there are appointments
    final dateKey = DateTime(date.year, date.month, date.day);
    final dayAppointments = _appointments[dateKey] ?? [];

    if (dayAppointments.isNotEmpty) {
      _showDayDetailBottomSheet(date, dayAppointments);
    }
  }

  void _showDayDetailBottomSheet(
      DateTime date, List<Map<String, dynamic>> appointments) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DayDetailBottomSheet(
        selectedDate: date,
        appointments: appointments,
        onAppointmentTap: _onAppointmentTap,
        onCompleteAppointment: _onCompleteAppointment,
        onRescheduleAppointment: _onRescheduleAppointment,
        onContactClient: _onContactClient,
      ),
    );
  }

  void _onAppointmentTap(Map<String, dynamic> appointment) {
    Navigator.pop(context); // Close bottom sheet
    Navigator.pushNamed(context, '/appointment-detail', arguments: appointment);
  }

  void _onAppointmentLongPress(Map<String, dynamic> appointment) {
    _showAppointmentContextMenu(appointment);
  }

  void _showAppointmentContextMenu(Map<String, dynamic> appointment) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12.w,
              height: 0.5.h,
              margin: EdgeInsets.symmetric(vertical: 1.h),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant
                    .withValues(alpha: 0.4),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(4.w),
              child: Column(
                children: [
                  Text(
                    appointment['clientName'] as String,
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  _buildContextMenuItem(
                    icon: 'visibility',
                    title: 'View Details',
                    onTap: () {
                      Navigator.pop(context);
                      _onAppointmentTap(appointment);
                    },
                  ),
                  _buildContextMenuItem(
                    icon: 'check_circle',
                    title: 'Mark as Complete',
                    onTap: () {
                      Navigator.pop(context);
                      _onCompleteAppointment(appointment);
                    },
                  ),
                  _buildContextMenuItem(
                    icon: 'schedule',
                    title: 'Reschedule',
                    onTap: () {
                      Navigator.pop(context);
                      _onRescheduleAppointment(appointment);
                    },
                  ),
                  _buildContextMenuItem(
                    icon: 'phone',
                    title: 'Contact Client',
                    onTap: () {
                      Navigator.pop(context);
                      _onContactClient(appointment);
                    },
                  ),
                  SizedBox(height: 2.h),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContextMenuItem({
    required String icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 2.h),
        child: Row(
          children: [
            CustomIconWidget(
              iconName: icon,
              color: AppTheme.lightTheme.colorScheme.primary,
              size: 24,
            ),
            SizedBox(width: 4.w),
            Text(
              title,
              style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onCompleteAppointment(Map<String, dynamic> appointment) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Appointment with ${appointment['clientName']} marked as complete'),
        backgroundColor: AppTheme.getSuccessColor(true),
      ),
    );
  }

  void _onRescheduleAppointment(Map<String, dynamic> appointment) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('Reschedule appointment with ${appointment['clientName']}'),
        backgroundColor: AppTheme.getWarningColor(true),
      ),
    );
  }

  void _onContactClient(Map<String, dynamic> appointment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Contact ${appointment['clientName']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Phone: ${appointment['phone']}'),
            SizedBox(height: 1.h),
            Text('Email: ${appointment['email']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Call'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Email'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _onQuickAddAppointment() {
    Navigator.pushNamed(context, '/add-edit-client', arguments: {
      'selectedDate': _selectedDate,
      'isQuickAdd': true,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Calendar',
          style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.lightTheme.appBarTheme.backgroundColor,
        elevation: 0,
        actions: [
          GestureDetector(
            onTap: _onQuickAddAppointment,
            child: Container(
              margin: EdgeInsets.only(right: 4.w),
              padding: EdgeInsets.all(2.w),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomIconWidget(
                iconName: 'add',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
            ),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Simulate refresh
          await Future.delayed(Duration(milliseconds: 500));
          setState(() {
            // Refresh appointment data
          });
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              // Calendar header
              CalendarHeaderWidget(
                currentMonth: _currentMonth,
                onPreviousMonth: _onPreviousMonth,
                onNextMonth: _onNextMonth,
                onTodayPressed: _onTodayPressed,
              ),

              // Calendar grid
              SlideTransition(
                position: _slideAnimation,
                child: CalendarGridWidget(
                  currentMonth: _currentMonth,
                  selectedDate: _selectedDate,
                  today: _today,
                  onDateSelected: _onDateSelected,
                  appointments: _appointments,
                ),
              ),

              SizedBox(height: 2.h),

              // Week view
              WeekViewWidget(
                selectedDate: _selectedDate,
                appointments: _appointments,
                onAppointmentTap: _onAppointmentTap,
                onAppointmentLongPress: _onAppointmentLongPress,
              ),

              SizedBox(height: 10.h), // Bottom padding for navigation
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _onQuickAddAppointment,
        backgroundColor:
            AppTheme.lightTheme.floatingActionButtonTheme.backgroundColor,
        child: CustomIconWidget(
          iconName: 'add',
          color: AppTheme.lightTheme.floatingActionButtonTheme.foregroundColor!,
          size: 24,
        ),
      ),
    );
  }
}
