import 'package:supabase_flutter/supabase_flutter.dart';

import './supabase_service.dart';

class ServiceTypeService {
  static final SupabaseClient _client = SupabaseService.instance.client;

  // Get all service types for the current user
  static Future<List<Map<String, dynamic>>> getAllServiceTypes() async {
    try {
      final response = await _client
          .from('service_types')
          .select()
          .eq('is_active', true)
          .order('name');
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch service types: $error');
    }
  }

  // Get service type by ID
  static Future<Map<String, dynamic>?> getServiceTypeById(
      String serviceTypeId) async {
    try {
      final response = await _client
          .from('service_types')
          .select()
          .eq('id', serviceTypeId)
          .single();
      return response;
    } catch (error) {
      throw Exception('Failed to fetch service type: $error');
    }
  }

  // Create new service type
  static Future<Map<String, dynamic>> createServiceType({
    required String name,
    String? description,
    int durationMinutes = 60,
    double? price,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final serviceTypeData = {
        'user_id': userId,
        'name': name,
        'description': description,
        'duration_minutes': durationMinutes,
        'price': price,
      };

      final response = await _client
          .from('service_types')
          .insert(serviceTypeData)
          .select()
          .single();
      return response;
    } catch (error) {
      throw Exception('Failed to create service type: $error');
    }
  }

  // Update service type
  static Future<Map<String, dynamic>> updateServiceType({
    required String serviceTypeId,
    String? name,
    String? description,
    int? durationMinutes,
    double? price,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (name != null) updateData['name'] = name;
      if (description != null) updateData['description'] = description;
      if (durationMinutes != null)
        updateData['duration_minutes'] = durationMinutes;
      if (price != null) updateData['price'] = price;

      final response = await _client
          .from('service_types')
          .update(updateData)
          .eq('id', serviceTypeId)
          .select()
          .single();
      return response;
    } catch (error) {
      throw Exception('Failed to update service type: $error');
    }
  }

  // Delete service type (soft delete)
  static Future<void> deleteServiceType(String serviceTypeId) async {
    try {
      await _client.from('service_types').update({
        'is_active': false,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', serviceTypeId);
    } catch (error) {
      throw Exception('Failed to delete service type: $error');
    }
  }

  // Get service types with usage statistics
  static Future<List<Map<String, dynamic>>> getServiceTypesWithStats() async {
    try {
      final serviceTypes = await getAllServiceTypes();

      for (var serviceType in serviceTypes) {
        final appointmentCount = await _client
            .from('appointments')
            .select('id')
            .eq('service_type_id', serviceType['id'])
            .count();

        serviceType['appointment_count'] = appointmentCount.count ?? 0;
      }

      return serviceTypes;
    } catch (error) {
      throw Exception('Failed to fetch service types with stats: $error');
    }
  }
}
