import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ClientCardWidget extends StatelessWidget {
  final Map<String, dynamic> client;
  final VoidCallback? onTap;
  final VoidCallback? onCall;
  final VoidCallback? onMessage;
  final VoidCallback? onSchedule;
  final VoidCallback? onEdit;
  final VoidCallback? onViewHistory;
  final VoidCallback? onDelete;

  const ClientCardWidget({
    Key? key,
    required this.client,
    this.onTap,
    this.onCall,
    this.onMessage,
    this.onSchedule,
    this.onEdit,
    this.onViewHistory,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Dismissible(
      key: Key('client_${client["id"]}'),
      background: _buildSwipeBackground(
        context,
        isDark,
        isLeftSwipe: false,
        icon: 'schedule',
        label: 'Schedule',
        color: AppTheme.getSuccessColor(isDark),
      ),
      secondaryBackground: _buildSwipeBackground(
        context,
        isDark,
        isLeftSwipe: true,
        icon: 'delete',
        label: 'Delete',
        color: AppTheme.getErrorColor(isDark),
      ),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.startToEnd) {
          onSchedule?.call();
          return false;
        } else {
          return await _showDeleteConfirmation(context);
        }
      },
      child: GestureDetector(
        onTap: onTap,
        onLongPress: () => _showContextMenu(context),
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: isDark
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(4.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            client["name"] as String? ?? "Unknown Client",
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 0.5.h),
                          Text(
                            client["primaryService"] as String? ?? "No Service",
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall
                                ?.copyWith(
                                  color:
                                      Theme.of(context).colorScheme.secondary,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        GestureDetector(
                          onTap: onCall,
                          child: Container(
                            padding: EdgeInsets.all(2.w),
                            decoration: BoxDecoration(
                              color: AppTheme.getSuccessColor(!isDark)
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: CustomIconWidget(
                              iconName: 'phone',
                              color: AppTheme.getSuccessColor(!isDark),
                              size: 18,
                            ),
                          ),
                        ),
                        SizedBox(width: 2.w),
                        GestureDetector(
                          onTap: onMessage,
                          child: Container(
                            padding: EdgeInsets.all(2.w),
                            decoration: BoxDecoration(
                              color: Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: CustomIconWidget(
                              iconName: 'message',
                              color: Theme.of(context).colorScheme.primary,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 2.h),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        'Last Service',
                        client["lastServiceDate"] as String? ?? "Never",
                        'history',
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 4.h,
                      color: Theme.of(context).dividerColor,
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        'Next Due',
                        client["nextDueDate"] as String? ?? "Not Scheduled",
                        'schedule',
                        isOverdue: _isOverdue(client["nextDueDate"] as String?),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSwipeBackground(
    BuildContext context,
    bool isDark, {
    required bool isLeftSwipe,
    required String icon,
    required String label,
    required Color color,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 6.w),
        child: Row(
          mainAxisAlignment:
              isLeftSwipe ? MainAxisAlignment.end : MainAxisAlignment.start,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomIconWidget(
                  iconName: icon,
                  color: Colors.white,
                  size: 24,
                ),
                SizedBox(height: 0.5.h),
                Text(
                  label,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    String label,
    String value,
    String iconName, {
    bool isOverdue = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 2.w),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomIconWidget(
                iconName: iconName,
                color: isOverdue
                    ? AppTheme.getErrorColor(
                        Theme.of(context).brightness == Brightness.light)
                    : Theme.of(context).colorScheme.secondary,
                size: 16,
              ),
              SizedBox(width: 1.w),
              Flexible(
                child: Text(
                  label,
                  style: Theme.of(context).textTheme.labelSmall,
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: 0.5.h),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: isOverdue
                      ? AppTheme.getErrorColor(
                          Theme.of(context).brightness == Brightness.light)
                      : Theme.of(context).colorScheme.onSurface,
                ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  bool _isOverdue(String? dateString) {
    if (dateString == null ||
        dateString == "Not Scheduled" ||
        dateString == "Never") {
      return false;
    }

    try {
      final dueDate = DateTime.parse(dateString);
      return dueDate.isBefore(DateTime.now());
    } catch (e) {
      return false;
    }
  }

  Future<bool> _showDeleteConfirmation(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(
                'Delete Client',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              content: Text(
                'Are you sure you want to delete ${client["name"]}? This action cannot be undone.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                    onDelete?.call();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.getErrorColor(
                        Theme.of(context).brightness == Brightness.light),
                  ),
                  child: Text('Delete'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  void _showContextMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(4.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 12.w,
                height: 0.5.h,
                decoration: BoxDecoration(
                  color: Theme.of(context).dividerColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                client["name"] as String? ?? "Client Options",
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              SizedBox(height: 2.h),
              _buildContextMenuItem(
                  context, 'Schedule Service', 'schedule', onSchedule),
              _buildContextMenuItem(context, 'Edit Client', 'edit', onEdit),
              _buildContextMenuItem(
                  context, 'View History', 'history', onViewHistory),
              _buildContextMenuItem(context, 'Call Client', 'phone', onCall),
              _buildContextMenuItem(
                  context, 'Message Client', 'message', onMessage),
              Divider(height: 3.h),
              _buildContextMenuItem(
                context,
                'Delete Client',
                'delete',
                () {
                  Navigator.pop(context);
                  _showDeleteConfirmation(context).then((confirmed) {
                    if (confirmed) onDelete?.call();
                  });
                },
                isDestructive: true,
              ),
              SizedBox(height: 2.h),
            ],
          ),
        );
      },
    );
  }

  Widget _buildContextMenuItem(
    BuildContext context,
    String title,
    String iconName,
    VoidCallback? onTap, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: CustomIconWidget(
        iconName: iconName,
        color: isDestructive
            ? AppTheme.getErrorColor(
                Theme.of(context).brightness == Brightness.light)
            : Theme.of(context).colorScheme.onSurface,
        size: 24,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: isDestructive
                  ? AppTheme.getErrorColor(
                      Theme.of(context).brightness == Brightness.light)
                  : Theme.of(context).colorScheme.onSurface,
            ),
      ),
      onTap: () {
        Navigator.pop(context);
        onTap?.call();
      },
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }
}
