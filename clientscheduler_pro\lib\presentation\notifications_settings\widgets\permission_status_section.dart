import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class PermissionStatusSection extends StatelessWidget {
  final bool isPermissionGranted;
  final VoidCallback onEnablePermissions;

  const PermissionStatusSection({
    Key? key,
    required this.isPermissionGranted,
    required this.onEnablePermissions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isPermissionGranted
            ? AppTheme.getSuccessColor(true).withValues(alpha: 0.05)
            : AppTheme.getWarningColor(true).withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isPermissionGranted
              ? AppTheme.getSuccessColor(true).withValues(alpha: 0.2)
              : AppTheme.getWarningColor(true).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: isPermissionGranted
                      ? AppTheme.getSuccessColor(true).withValues(alpha: 0.1)
                      : AppTheme.getWarningColor(true).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomIconWidget(
                  iconName: isPermissionGranted ? 'check_circle' : 'warning',
                  color: isPermissionGranted
                      ? AppTheme.getSuccessColor(true)
                      : AppTheme.getWarningColor(true),
                  size: 20,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Text(
                  'Notification Permission Status',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.lightTheme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppTheme.lightTheme.colorScheme.outline
                    .withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: isPermissionGranted
                            ? AppTheme.getSuccessColor(true)
                            : AppTheme.getWarningColor(true),
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      isPermissionGranted
                          ? 'Notifications Enabled'
                          : 'Notifications Disabled',
                      style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                        color: isPermissionGranted
                            ? AppTheme.getSuccessColor(true)
                            : AppTheme.getWarningColor(true),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 1.h),
                Text(
                  isPermissionGranted
                      ? 'ClientScheduler Pro can send you notifications about your daily schedule and appointments.'
                      : 'To receive notifications, you need to enable notification permissions in your device settings.',
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          isPermissionGranted
              ? const SizedBox.shrink()
              : Column(
                  children: [
                    SizedBox(height: 2.h),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: onEnablePermissions,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.getWarningColor(true),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: EdgeInsets.symmetric(vertical: 2.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomIconWidget(
                              iconName: 'settings',
                              color: Colors.white,
                              size: 20,
                            ),
                            SizedBox(width: 2.w),
                            Text(
                              'Enable in Settings',
                              style: AppTheme.lightTheme.textTheme.bodyMedium
                                  ?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }
}
