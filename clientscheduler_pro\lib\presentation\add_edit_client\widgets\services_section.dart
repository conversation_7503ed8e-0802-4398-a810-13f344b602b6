import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ServicesSection extends StatefulWidget {
  final List<Map<String, dynamic>> selectedServices;
  final Function(List<Map<String, dynamic>>) onServicesChanged;

  const ServicesSection({
    Key? key,
    required this.selectedServices,
    required this.onServicesChanged,
  }) : super(key: key);

  @override
  State<ServicesSection> createState() => _ServicesSectionState();
}

class _ServicesSectionState extends State<ServicesSection> {
  final List<Map<String, dynamic>> availableServices = [
    {
      'id': 1,
      'name': 'House Cleaning',
      'description': 'Complete home cleaning service',
      'defaultSchedule': 'Weekly',
    },
    {
      'id': 2,
      'name': 'Lawn Care',
      'description': 'Lawn mowing and maintenance',
      'defaultSchedule': 'Bi-weekly',
    },
    {
      'id': 3,
      'name': 'Pool Maintenance',
      'description': 'Pool cleaning and chemical balancing',
      'defaultSchedule': 'Weekly',
    },
    {
      'id': 4,
      'name': 'HVAC Service',
      'description': 'Heating and cooling system maintenance',
      'defaultSchedule': 'Quarterly',
    },
    {
      'id': 5,
      'name': 'Pest Control',
      'description': 'Residential pest control service',
      'defaultSchedule': 'Monthly',
    },
    {
      'id': 6,
      'name': 'Window Cleaning',
      'description': 'Interior and exterior window cleaning',
      'defaultSchedule': 'Monthly',
    },
  ];

  final List<String> scheduleOptions = [
    'Weekly',
    'Bi-weekly',
    'Monthly',
    'Quarterly',
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Services',
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.lightTheme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 3.h),

          // Selected Services
          if (widget.selectedServices.isNotEmpty) ...[
            Wrap(
              spacing: 2.w,
              runSpacing: 1.h,
              children: widget.selectedServices.map((service) {
                return _buildServiceChip(service);
              }).toList(),
            ),
            SizedBox(height: 3.h),
          ],

          // Add Service Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showServiceSelectionBottomSheet(),
              icon: CustomIconWidget(
                iconName: 'add',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 5.w,
              ),
              label: Text('Add Service'),
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 2.h),
              ),
            ),
          ),

          if (widget.selectedServices.isEmpty)
            Padding(
              padding: EdgeInsets.only(top: 2.h),
              child: Text(
                'No services selected. Add services to schedule recurring appointments.',
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildServiceChip(Map<String, dynamic> service) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                service['name'] as String,
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: AppTheme.lightTheme.colorScheme.primary,
                ),
              ),
              Text(
                '${service['schedule']} • Next: ${service['nextAppointment']}',
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.primary
                      .withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
          SizedBox(width: 2.w),
          GestureDetector(
            onTap: () => _editService(service),
            child: CustomIconWidget(
              iconName: 'edit',
              color: AppTheme.lightTheme.colorScheme.primary,
              size: 4.w,
            ),
          ),
          SizedBox(width: 1.w),
          GestureDetector(
            onTap: () => _removeService(service),
            child: CustomIconWidget(
              iconName: 'close',
              color: AppTheme.lightTheme.colorScheme.error,
              size: 4.w,
            ),
          ),
        ],
      ),
    );
  }

  void _showServiceSelectionBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) => Container(
          padding: EdgeInsets.all(4.w),
          child: Column(
            children: [
              Container(
                width: 12.w,
                height: 0.5.h,
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.colorScheme.outline,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                'Select Services',
                style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 3.h),
              Expanded(
                child: ListView.separated(
                  controller: scrollController,
                  itemCount: availableServices.length,
                  separatorBuilder: (context, index) => SizedBox(height: 1.h),
                  itemBuilder: (context, index) {
                    final service = availableServices[index];
                    final isSelected = widget.selectedServices.any(
                      (selected) => selected['id'] == service['id'],
                    );

                    return _buildServiceOption(service, isSelected);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildServiceOption(Map<String, dynamic> service, bool isSelected) {
    return Container(
      decoration: BoxDecoration(
        color: isSelected
            ? AppTheme.lightTheme.colorScheme.primaryContainer
                .withValues(alpha: 0.3)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? AppTheme.lightTheme.colorScheme.primary
              : AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: ListTile(
        leading: Container(
          width: 10.w,
          height: 10.w,
          decoration: BoxDecoration(
            color: isSelected
                ? AppTheme.lightTheme.colorScheme.primary
                : AppTheme.lightTheme.colorScheme.primaryContainer,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: CustomIconWidget(
              iconName: isSelected ? 'check' : 'home_repair_service',
              color: isSelected
                  ? Colors.white
                  : AppTheme.lightTheme.colorScheme.primary,
              size: 5.w,
            ),
          ),
        ),
        title: Text(
          service['name'] as String,
          style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          service['description'] as String,
          style: AppTheme.lightTheme.textTheme.bodySmall,
        ),
        trailing: isSelected
            ? CustomIconWidget(
                iconName: 'check_circle',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 6.w,
              )
            : null,
        onTap: () {
          if (isSelected) {
            _removeServiceById(service['id'] as int);
          } else {
            _addService(service);
          }
        },
      ),
    );
  }

  void _addService(Map<String, dynamic> service) {
    final newService = {
      'id': service['id'],
      'name': service['name'],
      'description': service['description'],
      'schedule': service['defaultSchedule'],
      'nextAppointment':
          _calculateNextAppointment(service['defaultSchedule'] as String),
    };

    final updatedServices =
        List<Map<String, dynamic>>.from(widget.selectedServices);
    updatedServices.add(newService);
    widget.onServicesChanged(updatedServices);

    Navigator.pop(context);
  }

  void _removeService(Map<String, dynamic> service) {
    final updatedServices =
        widget.selectedServices.where((s) => s['id'] != service['id']).toList();
    widget.onServicesChanged(updatedServices);
  }

  void _removeServiceById(int serviceId) {
    final updatedServices =
        widget.selectedServices.where((s) => s['id'] != serviceId).toList();
    widget.onServicesChanged(updatedServices);
  }

  void _editService(Map<String, dynamic> service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit Service Schedule'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              service['name'] as String,
              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            Text('Recurring Schedule:'),
            SizedBox(height: 1.h),
            ...scheduleOptions.map((option) {
              return RadioListTile<String>(
                title: Text(option),
                value: option,
                groupValue: service['schedule'] as String,
                onChanged: (value) {
                  if (value != null) {
                    _updateServiceSchedule(service, value);
                    Navigator.pop(context);
                  }
                },
              );
            }).toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _updateServiceSchedule(
      Map<String, dynamic> service, String newSchedule) {
    final updatedServices = widget.selectedServices.map((s) {
      if (s['id'] == service['id']) {
        return {
          ...s,
          'schedule': newSchedule,
          'nextAppointment': _calculateNextAppointment(newSchedule),
        };
      }
      return s;
    }).toList();

    widget.onServicesChanged(updatedServices);
  }

  String _calculateNextAppointment(String schedule) {
    final now = DateTime.now();
    DateTime nextDate;

    switch (schedule) {
      case 'Weekly':
        nextDate = now.add(Duration(days: 7));
        break;
      case 'Bi-weekly':
        nextDate = now.add(Duration(days: 14));
        break;
      case 'Monthly':
        nextDate = DateTime(now.year, now.month + 1, now.day);
        break;
      case 'Quarterly':
        nextDate = DateTime(now.year, now.month + 3, now.day);
        break;
      default:
        nextDate = now.add(Duration(days: 7));
    }

    return '${nextDate.month}/${nextDate.day}/${nextDate.year}';
  }
}
