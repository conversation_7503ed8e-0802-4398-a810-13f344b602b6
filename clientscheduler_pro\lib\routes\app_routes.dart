import 'package:flutter/material.dart';
import '../presentation/client_list/client_list.dart';
import '../presentation/add_edit_client/add_edit_client.dart';
import '../presentation/calendar_view/calendar_view.dart';
import '../presentation/notifications_settings/notifications_settings.dart';
import '../presentation/appointment_detail/appointment_detail.dart';

class AppRoutes {
  // TODO: Add your routes here
  static const String initial = '/';
  static const String clientList = '/client-list';
  static const String addEditClient = '/add-edit-client';
  static const String dashboard = '/dashboard';
  static const String calendarView = '/calendar-view';
  static const String notificationsSettings = '/notifications-settings';
  static const String appointmentDetail = '/appointment-detail';

  static Map<String, WidgetBuilder> routes = {
  initial: (context) => const ClientList(),
  clientList: (context) => const ClientList(),
  addEditClient: (context) => const AddEditClient(),
  calendarView: (context) => const CalendarView(),
  notificationsSettings: (context) => const NotificationsSettings(),
  appointmentDetail: (context) => const AppointmentDetail(),
  // TODO: Add your other routes here
};
}
