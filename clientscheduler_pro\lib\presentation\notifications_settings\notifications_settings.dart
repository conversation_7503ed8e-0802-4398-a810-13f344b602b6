import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/advanced_settings_section.dart';
import './widgets/client_notifications_section.dart';
import './widgets/daily_notifications_section.dart';
import './widgets/notification_types_section.dart';
import './widgets/permission_status_section.dart';

class NotificationsSettings extends StatefulWidget {
  const NotificationsSettings({Key? key}) : super(key: key);

  @override
  State<NotificationsSettings> createState() => _NotificationsSettingsState();
}

class _NotificationsSettingsState extends State<NotificationsSettings> {
  // Daily notifications settings
  bool _isDailyNotificationsEnabled = true;
  TimeOfDay _notificationTime = const TimeOfDay(hour: 8, minute: 0);

  // Client notifications settings
  List<Map<String, dynamic>> _clients = [
    {
      'id': '1',
      'name': '<PERSON>',
      'service': 'House Cleaning',
      'notificationsEnabled': true,
    },
    {
      'id': '2',
      'name': '<PERSON>',
      'service': 'Lawn Care',
      'notificationsEnabled': true,
    },
    {
      'id': '3',
      'name': '<PERSON>',
      'service': 'Home Inspection',
      'notificationsEnabled': false,
    },
    {
      'id': '4',
      'name': 'Robert Brown',
      'service': 'Pool Maintenance',
      'notificationsEnabled': true,
    },
    {
      'id': '5',
      'name': 'Lisa Anderson',
      'service': 'Garden Care',
      'notificationsEnabled': true,
    },
  ];

  // Notification types settings
  Map<String, bool> _notificationTypes = {
    'dailyReminders': true,
    'overdueAlerts': true,
    'upcomingWarnings': false,
    'completionReminders': true,
  };

  // Advanced settings
  String _selectedSound = 'default';
  bool _isVibrationEnabled = true;
  bool _isBadgeCountEnabled = true;

  // Permission status
  bool _isPermissionGranted = true;

  @override
  void initState() {
    super.initState();
    _checkNotificationPermissions();
  }

  Future<void> _checkNotificationPermissions() async {
    // Simulate permission check
    await Future.delayed(const Duration(milliseconds: 500));
    setState(() {
      _isPermissionGranted = true; // Mock permission status
    });
  }

  void _onDailyNotificationsToggle(bool value) {
    setState(() {
      _isDailyNotificationsEnabled = value;
    });
    _saveSettings();
  }

  void _onNotificationTimeChanged(TimeOfDay time) {
    setState(() {
      _notificationTime = time;
    });
    _saveSettings();
  }

  void _onClientToggleChanged(String clientId, bool value) {
    setState(() {
      final clientIndex =
          _clients.indexWhere((client) => client['id'] == clientId);
      if (clientIndex != -1) {
        _clients[clientIndex]['notificationsEnabled'] = value;
      }
    });
    _saveSettings();
  }

  void _onNotificationTypeToggleChanged(String type, bool value) {
    setState(() {
      _notificationTypes[type] = value;
    });
    _saveSettings();
  }

  void _onSoundChanged(String sound) {
    setState(() {
      _selectedSound = sound;
    });
    _saveSettings();
  }

  void _onVibrationChanged(bool value) {
    setState(() {
      _isVibrationEnabled = value;
    });
    _saveSettings();
  }

  void _onBadgeCountChanged(bool value) {
    setState(() {
      _isBadgeCountEnabled = value;
    });
    _saveSettings();
  }

  void _onTestNotification() {
    // Send test notification
    Fluttertoast.showToast(
      msg: "Test notification sent! Check your notification panel.",
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppTheme.lightTheme.primaryColor,
      textColor: Colors.white,
      fontSize: 14.sp,
    );
  }

  void _onEnablePermissions() {
    // Open system settings for notification permissions
    Fluttertoast.showToast(
      msg: "Opening system settings to enable notifications...",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppTheme.getWarningColor(true),
      textColor: Colors.white,
      fontSize: 14.sp,
    );

    // Simulate permission granted after user returns
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isPermissionGranted = true;
        });
      }
    });
  }

  void _saveSettings() {
    // Save settings to local storage
    Fluttertoast.showToast(
      msg: "Settings saved",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppTheme.getSuccessColor(true),
      textColor: Colors.white,
      fontSize: 12.sp,
    );
  }

  void _openSystemSettings() {
    // Open system notification settings
    Fluttertoast.showToast(
      msg: "Opening system notification settings...",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppTheme.lightTheme.primaryColor,
      textColor: Colors.white,
      fontSize: 14.sp,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.lightTheme.appBarTheme.backgroundColor,
        elevation: AppTheme.lightTheme.appBarTheme.elevation,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            color: AppTheme.lightTheme.colorScheme.onSurface,
            size: 24,
          ),
        ),
        title: Text(
          'Notification Settings',
          style: AppTheme.lightTheme.appBarTheme.titleTextStyle,
        ),
        actions: [
          IconButton(
            onPressed: _openSystemSettings,
            icon: CustomIconWidget(
              iconName: 'settings',
              color: AppTheme.lightTheme.colorScheme.onSurface,
              size: 24,
            ),
            tooltip: 'System Settings',
          ),
        ],
      ),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 2.h),

            // Permission Status Section
            PermissionStatusSection(
              isPermissionGranted: _isPermissionGranted,
              onEnablePermissions: _onEnablePermissions,
            ),

            // Daily Notifications Section
            DailyNotificationsSection(
              isDailyNotificationsEnabled: _isDailyNotificationsEnabled,
              notificationTime: _notificationTime,
              onToggleChanged: _onDailyNotificationsToggle,
              onTimeChanged: _onNotificationTimeChanged,
            ),

            // Individual Client Notifications Section
            ClientNotificationsSection(
              clients: _clients,
              onClientToggleChanged: _onClientToggleChanged,
            ),

            // Notification Types Section
            NotificationTypesSection(
              notificationTypes: _notificationTypes,
              onTypeToggleChanged: _onNotificationTypeToggleChanged,
            ),

            // Advanced Settings Section
            AdvancedSettingsSection(
              selectedSound: _selectedSound,
              isVibrationEnabled: _isVibrationEnabled,
              isBadgeCountEnabled: _isBadgeCountEnabled,
              onSoundChanged: _onSoundChanged,
              onVibrationChanged: _onVibrationChanged,
              onBadgeCountChanged: _onBadgeCountChanged,
              onTestNotification: _onTestNotification,
            ),

            SizedBox(height: 4.h),

            // Footer Information
            Container(
              width: double.infinity,
              margin: EdgeInsets.symmetric(horizontal: 4.w),
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color:
                      AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CustomIconWidget(
                        iconName: 'info',
                        color: AppTheme.lightTheme.primaryColor,
                        size: 16,
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        'About Notifications',
                        style:
                            AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.lightTheme.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    'Notifications help you stay on top of your client schedule and never miss an appointment. All settings are saved automatically and take effect immediately.',
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 4.h),
          ],
        ),
      ),
    );
  }
}
