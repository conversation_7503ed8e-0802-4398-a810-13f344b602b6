import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/address_section.dart';
import './widgets/notes_section.dart';
import './widgets/personal_info_section.dart';
import './widgets/services_section.dart';

class AddEditClient extends StatefulWidget {
  final Map<String, dynamic>? clientData;

  const AddEditClient({
    Key? key,
    this.clientData,
  }) : super(key: key);

  @override
  State<AddEditClient> createState() => _AddEditClientState();
}

class _AddEditClientState extends State<AddEditClient> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  // Controllers
  late TextEditingController _nameController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _streetController;
  late TextEditingController _cityController;
  late TextEditingController _stateController;
  late TextEditingController _zipController;
  late TextEditingController _notesController;

  // State variables
  String? _profileImagePath;
  List<Map<String, dynamic>> _selectedServices = [];
  bool _hasUnsavedChanges = false;
  bool _isLoading = false;

  // Validation errors
  String? _nameError;
  String? _phoneError;
  String? _emailError;
  String? _streetError;
  String? _cityError;
  String? _stateError;
  String? _zipError;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadClientData();
  }

  void _initializeControllers() {
    _nameController = TextEditingController();
    _phoneController = TextEditingController();
    _emailController = TextEditingController();
    _streetController = TextEditingController();
    _cityController = TextEditingController();
    _stateController = TextEditingController();
    _zipController = TextEditingController();
    _notesController = TextEditingController();

    // Add listeners for unsaved changes detection
    _nameController.addListener(_onFieldChanged);
    _phoneController.addListener(_onFieldChanged);
    _emailController.addListener(_onFieldChanged);
    _streetController.addListener(_onFieldChanged);
    _cityController.addListener(_onFieldChanged);
    _stateController.addListener(_onFieldChanged);
    _zipController.addListener(_onFieldChanged);
    _notesController.addListener(_onFieldChanged);
  }

  void _loadClientData() {
    if (widget.clientData != null) {
      final client = widget.clientData!;
      _nameController.text = client['name'] ?? '';
      _phoneController.text = client['phone'] ?? '';
      _emailController.text = client['email'] ?? '';
      _streetController.text = client['street'] ?? '';
      _cityController.text = client['city'] ?? '';
      _stateController.text = client['state'] ?? '';
      _zipController.text = client['zip'] ?? '';
      _notesController.text = client['notes'] ?? '';
      _profileImagePath = client['profileImage'];
      _selectedServices =
          List<Map<String, dynamic>>.from(client['services'] ?? []);
    }
  }

  void _onFieldChanged() {
    if (!_hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = true;
      });
    }
    _clearValidationErrors();
  }

  void _clearValidationErrors() {
    setState(() {
      _nameError = null;
      _phoneError = null;
      _emailError = null;
      _streetError = null;
      _cityError = null;
      _stateError = null;
      _zipError = null;
    });
  }

  bool get _isEditing => widget.clientData != null;

  bool get _isFormValid {
    return _nameController.text.trim().isNotEmpty &&
        _phoneController.text.trim().isNotEmpty &&
        _isValidEmail(_emailController.text.trim());
  }

  bool _isValidEmail(String email) {
    if (email.isEmpty) return true; // Email is optional
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool _isValidPhone(String phone) {
    final digitsOnly = phone.replaceAll(RegExp(r'[^\d]'), '');
    return digitsOnly.length == 10;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(_isEditing ? 'Edit Client' : 'Add New Client'),
      leading: IconButton(
        onPressed: _onCancelPressed,
        icon: CustomIconWidget(
          iconName: 'close',
          color: AppTheme.lightTheme.colorScheme.onSurface,
          size: 6.w,
        ),
      ),
      actions: [
        Padding(
          padding: EdgeInsets.only(right: 4.w),
          child: ElevatedButton(
            onPressed: _isFormValid && !_isLoading ? _onSavePressed : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _isFormValid
                  ? AppTheme.lightTheme.colorScheme.primary
                  : AppTheme.lightTheme.colorScheme.outline
                      .withValues(alpha: 0.3),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.5.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    width: 4.w,
                    height: 4.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'Save',
                    style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        controller: _scrollController,
        physics: BouncingScrollPhysics(),
        child: Column(
          children: [
            SizedBox(height: 2.h),

            // Personal Information Section
            PersonalInfoSection(
              nameController: _nameController,
              phoneController: _phoneController,
              emailController: _emailController,
              profileImagePath: _profileImagePath,
              onImageSelected: (imagePath) {
                setState(() {
                  _profileImagePath = imagePath;
                  _hasUnsavedChanges = true;
                });
              },
              nameError: _nameError,
              phoneError: _phoneError,
              emailError: _emailError,
            ),

            // Address Section
            AddressSection(
              streetController: _streetController,
              cityController: _cityController,
              stateController: _stateController,
              zipController: _zipController,
              streetError: _streetError,
              cityError: _cityError,
              stateError: _stateError,
              zipError: _zipError,
            ),

            // Services Section
            ServicesSection(
              selectedServices: _selectedServices,
              onServicesChanged: (services) {
                setState(() {
                  _selectedServices = services;
                  _hasUnsavedChanges = true;
                });
              },
            ),

            // Notes Section
            NotesSection(
              notesController: _notesController,
            ),

            SizedBox(height: 4.h),
          ],
        ),
      ),
    );
  }

  Future<bool> _onWillPop() async {
    if (_hasUnsavedChanges) {
      return await _showUnsavedChangesDialog() ?? false;
    }
    return true;
  }

  Future<bool?> _showUnsavedChangesDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Unsaved Changes',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'You have unsaved changes. Are you sure you want to leave without saving?',
          style: AppTheme.lightTheme.textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Stay'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.lightTheme.colorScheme.error,
            ),
            child: Text('Leave'),
          ),
        ],
      ),
    );
  }

  void _onCancelPressed() async {
    if (_hasUnsavedChanges) {
      final shouldLeave = await _showUnsavedChangesDialog();
      if (shouldLeave == true) {
        Navigator.pop(context);
      }
    } else {
      Navigator.pop(context);
    }
  }

  void _onSavePressed() async {
    if (!_validateForm()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate API call
      await Future.delayed(Duration(milliseconds: 1500));

      final clientData = {
        'id': _isEditing
            ? widget.clientData!['id']
            : DateTime.now().millisecondsSinceEpoch,
        'name': _nameController.text.trim(),
        'phone': _phoneController.text.trim(),
        'email': _emailController.text.trim(),
        'street': _streetController.text.trim(),
        'city': _cityController.text.trim(),
        'state': _stateController.text.trim().toUpperCase(),
        'zip': _zipController.text.trim(),
        'notes': _notesController.text.trim(),
        'profileImage': _profileImagePath,
        'services': _selectedServices,
        'createdAt': _isEditing
            ? widget.clientData!['createdAt']
            : DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isEditing
                ? 'Client updated successfully!'
                : 'Client added successfully!',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: Colors.white,
            ),
          ),
          backgroundColor: AppTheme.lightTheme.colorScheme.primary,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          margin: EdgeInsets.all(4.w),
        ),
      );

      // Return to previous screen with result
      Navigator.pop(context, clientData);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Failed to save client. Please try again.',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: Colors.white,
            ),
          ),
          backgroundColor: AppTheme.lightTheme.colorScheme.error,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          margin: EdgeInsets.all(4.w),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  bool _validateForm() {
    bool isValid = true;

    // Validate name
    if (_nameController.text.trim().isEmpty) {
      setState(() {
        _nameError = 'Name is required';
      });
      isValid = false;
    }

    // Validate phone
    if (_phoneController.text.trim().isEmpty) {
      setState(() {
        _phoneError = 'Phone number is required';
      });
      isValid = false;
    } else if (!_isValidPhone(_phoneController.text)) {
      setState(() {
        _phoneError = 'Please enter a valid 10-digit phone number';
      });
      isValid = false;
    }

    // Validate email (if provided)
    if (_emailController.text.trim().isNotEmpty &&
        !_isValidEmail(_emailController.text.trim())) {
      setState(() {
        _emailError = 'Please enter a valid email address';
      });
      isValid = false;
    }

    // Validate state (if provided)
    if (_stateController.text.trim().isNotEmpty &&
        _stateController.text.trim().length != 2) {
      setState(() {
        _stateError = 'Please enter a valid 2-letter state code';
      });
      isValid = false;
    }

    // Validate ZIP (if provided)
    if (_zipController.text.trim().isNotEmpty &&
        _zipController.text.trim().length != 5) {
      setState(() {
        _zipError = 'Please enter a valid 5-digit ZIP code';
      });
      isValid = false;
    }

    return isValid;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _streetController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipController.dispose();
    _notesController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
