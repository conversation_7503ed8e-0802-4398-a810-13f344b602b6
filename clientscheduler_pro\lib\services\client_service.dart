import 'package:supabase_flutter/supabase_flutter.dart';

import './supabase_service.dart';

class ClientService {
  static final SupabaseClient _client = SupabaseService.instance.client;

  // Get all clients for the current user
  static Future<List<Map<String, dynamic>>> getAllClients() async {
    try {
      final response = await _client
          .from('clients')
          .select()
          .eq('is_active', true)
          .order('full_name');
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch clients: $error');
    }
  }

  // Get client by ID
  static Future<Map<String, dynamic>?> getClientById(String clientId) async {
    try {
      final response =
          await _client.from('clients').select().eq('id', clientId).single();
      return response;
    } catch (error) {
      throw Exception('Failed to fetch client: $error');
    }
  }

  // Create new client
  static Future<Map<String, dynamic>> createClient({
    required String fullName,
    String? email,
    String? phoneNumber,
    String? address,
    String? notes,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final clientData = {
        'user_id': userId,
        'full_name': fullName,
        'email': email,
        'phone_number': phoneNumber,
        'address': address,
        'notes': notes,
      };

      final response =
          await _client.from('clients').insert(clientData).select().single();
      return response;
    } catch (error) {
      throw Exception('Failed to create client: $error');
    }
  }

  // Update client
  static Future<Map<String, dynamic>> updateClient({
    required String clientId,
    String? fullName,
    String? email,
    String? phoneNumber,
    String? address,
    String? notes,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (fullName != null) updateData['full_name'] = fullName;
      if (email != null) updateData['email'] = email;
      if (phoneNumber != null) updateData['phone_number'] = phoneNumber;
      if (address != null) updateData['address'] = address;
      if (notes != null) updateData['notes'] = notes;

      final response = await _client
          .from('clients')
          .update(updateData)
          .eq('id', clientId)
          .select()
          .single();
      return response;
    } catch (error) {
      throw Exception('Failed to update client: $error');
    }
  }

  // Delete client (soft delete)
  static Future<void> deleteClient(String clientId) async {
    try {
      await _client.from('clients').update({
        'is_active': false,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', clientId);
    } catch (error) {
      throw Exception('Failed to delete client: $error');
    }
  }

  // Search clients by name
  static Future<List<Map<String, dynamic>>> searchClients(
      String searchQuery) async {
    try {
      final response = await _client
          .from('clients')
          .select()
          .eq('is_active', true)
          .ilike('full_name', '%$searchQuery%')
          .order('full_name');
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to search clients: $error');
    }
  }

  // Get client statistics
  static Future<Map<String, int>> getClientStatistics() async {
    try {
      final totalClientsResponse = await _client
          .from('clients')
          .select('id')
          .eq('is_active', true)
          .count();

      final activeAppointmentsResponse = await _client
          .from('appointments')
          .select('id')
          .inFilter(
              'status', ['scheduled', 'confirmed', 'in_progress']).count();

      return {
        'total_clients': totalClientsResponse.count ?? 0,
        'active_appointments': activeAppointmentsResponse.count ?? 0,
      };
    } catch (error) {
      throw Exception('Failed to fetch client statistics: $error');
    }
  }
}
