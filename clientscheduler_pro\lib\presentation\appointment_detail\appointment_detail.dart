import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/action_buttons_widget.dart';
import './widgets/client_header_widget.dart';
import './widgets/client_info_card_widget.dart';
import './widgets/photo_attachment_widget.dart';
import './widgets/recurring_schedule_widget.dart';
import './widgets/service_hero_card_widget.dart';
import './widgets/service_history_widget.dart';

class AppointmentDetail extends StatefulWidget {
  const AppointmentDetail({Key? key}) : super(key: key);

  @override
  State<AppointmentDetail> createState() => _AppointmentDetailState();
}

class _AppointmentDetailState extends State<AppointmentDetail> {
  late Map<String, dynamic> _appointmentData;
  late Map<String, dynamic> _clientData;
  late Map<String, dynamic> _scheduleData;
  late List<Map<String, dynamic>> _serviceHistory;
  List<String> _servicePhotos = [];

  @override
  void initState() {
    super.initState();
    _initializeMockData();
  }

  void _initializeMockData() {
    _clientData = {
      "id": 1,
      "name": "Sarah Johnson",
      "phone": "+****************",
      "email": "<EMAIL>",
      "address": "1234 Oak Street, Springfield, IL 62701",
      "notes":
          "Prefers morning appointments. Has two dogs that need to be secured during service visits.",
    };

    _appointmentData = {
      "id": 101,
      "clientId": 1,
      "serviceType": "Quarterly Home Maintenance",
      "date": DateTime(2025, 8, 15),
      "time": "10:00 AM",
      "status": "scheduled",
      "duration": 120,
      "notes": "Check HVAC filters, inspect gutters, test smoke detectors",
    };

    _scheduleData = {
      "isRecurring": true,
      "frequency": "quarterly",
      "nextAppointment": DateTime(2025, 11, 15),
      "startDate": DateTime(2024, 8, 15),
    };

    _serviceHistory = [
      {
        "id": 1,
        "serviceType": "Quarterly Home Maintenance",
        "completionDate": DateTime(2025, 5, 15, 14, 30),
        "status": "completed",
        "notes":
            "Replaced HVAC filters, cleaned gutters, all smoke detectors working properly. Recommended gutter guard installation.",
      },
      {
        "id": 2,
        "serviceType": "Quarterly Home Maintenance",
        "completionDate": DateTime(2025, 2, 15, 9, 45),
        "status": "completed",
        "notes":
            "Routine maintenance completed. Found minor leak in bathroom faucet - client will schedule separate repair.",
      },
      {
        "id": 3,
        "serviceType": "Initial Home Assessment",
        "completionDate": DateTime(2024, 11, 20, 16, 15),
        "status": "completed",
        "notes":
            "Comprehensive home assessment completed. Created maintenance schedule and priority list for upcoming services.",
      },
    ];

    _servicePhotos = [
      "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop",
      "https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=400&h=400&fit=crop",
    ];
  }

  void _handleEditClient() {
    Navigator.pushNamed(context, '/add-edit-client');
  }

  void _handleMarkComplete() {
    _showCompletionForm();
  }

  void _showCompletionForm() {
    final TextEditingController notesController = TextEditingController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          padding: EdgeInsets.all(4.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 12.w,
                height: 0.5.h,
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.colorScheme.outline
                      .withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
                margin: EdgeInsets.only(bottom: 2.h),
                alignment: Alignment.center,
              ),
              Text(
                'Complete Service',
                style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 2.h),
              TextField(
                controller: notesController,
                maxLines: 4,
                decoration: InputDecoration(
                  labelText: 'Service Notes (Optional)',
                  hintText: 'Add any notes about the completed service...',
                ),
              ),
              SizedBox(height: 3.h),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('Cancel'),
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _completeAppointment(notesController.text);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.getSuccessColor(true),
                      ),
                      child: Text('Complete'),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 2.h),
            ],
          ),
        ),
      ),
    );
  }

  void _completeAppointment(String notes) {
    HapticFeedback.mediumImpact();

    setState(() {
      _appointmentData['status'] = 'completed';
      _appointmentData['completionDate'] = DateTime.now();
      _appointmentData['completionNotes'] = notes;

      // Add to service history
      _serviceHistory.insert(0, {
        "id": _serviceHistory.length + 1,
        "serviceType": _appointmentData['serviceType'],
        "completionDate": DateTime.now(),
        "status": "completed",
        "notes": notes.isNotEmpty ? notes : "Service completed successfully.",
      });

      // Schedule next appointment
      _scheduleData['nextAppointment'] =
          DateTime.now().add(const Duration(days: 90));
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Appointment marked as completed'),
        backgroundColor: AppTheme.getSuccessColor(true),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _handleReschedule() {
    _showDateTimePicker();
  }

  void _showDateTimePicker() {
    showDatePicker(
      context: context,
      initialDate: _appointmentData['date'] as DateTime,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    ).then((selectedDate) {
      if (selectedDate != null) {
        _showTimePicker(selectedDate);
      }
    });
  }

  void _showTimePicker(DateTime selectedDate) {
    showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    ).then((selectedTime) {
      if (selectedTime != null) {
        setState(() {
          _appointmentData['date'] = selectedDate;
          _appointmentData['time'] = selectedTime.format(context);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Appointment rescheduled successfully'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    });
  }

  void _handleCancelAppointment() {
    setState(() {
      _appointmentData['status'] = 'cancelled';
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Appointment cancelled'),
        backgroundColor: AppTheme.getErrorColor(true),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _handleModifySchedule() {
    _showScheduleOptions();
  }

  void _showScheduleOptions() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12.w,
              height: 0.5.h,
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.outline
                    .withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              'Modify Schedule',
              style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            ..._buildFrequencyOptions(),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildFrequencyOptions() {
    final frequencies = [
      {'value': 'weekly', 'label': 'Weekly'},
      {'value': 'biweekly', 'label': 'Every 2 weeks'},
      {'value': 'monthly', 'label': 'Monthly'},
      {'value': 'quarterly', 'label': 'Every 3 months'},
      {'value': 'biannually', 'label': 'Every 6 months'},
      {'value': 'annually', 'label': 'Annually'},
    ];

    return frequencies
        .map((freq) => ListTile(
              title: Text(freq['label'] as String),
              trailing: _scheduleData['frequency'] == freq['value']
                  ? CustomIconWidget(
                      iconName: 'check',
                      color: AppTheme.lightTheme.colorScheme.primary,
                      size: 20,
                    )
                  : null,
              onTap: () {
                setState(() {
                  _scheduleData['frequency'] = freq['value'];
                });
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Schedule updated to ${freq['label']}'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ))
        .toList();
  }

  void _shareAppointmentDetails() {
    final String appointmentDetails = '''
Appointment Details

Client: ${_clientData['name']}
Service: ${_appointmentData['serviceType']}
Date: ${DateFormat('EEEE, MMMM d, yyyy').format(_appointmentData['date'] as DateTime)}
Time: ${_appointmentData['time']}
Status: ${_appointmentData['status']}

Contact: ${_clientData['phone']}
Address: ${_clientData['address']}
''';

    Share.share(appointmentDetails,
        subject: 'Appointment Details - ${_clientData['name']}');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('Appointment Details'),
        actions: [
          IconButton(
            onPressed: _shareAppointmentDetails,
            icon: CustomIconWidget(
              iconName: 'share',
              color: AppTheme.lightTheme.colorScheme.onSurface,
              size: 24,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  ClientHeaderWidget(
                    clientData: _clientData,
                    onEditPressed: _handleEditClient,
                  ),
                  ServiceHeroCardWidget(
                    appointmentData: _appointmentData,
                  ),
                  ClientInfoCardWidget(
                    clientData: _clientData,
                  ),
                  PhotoAttachmentWidget(
                    existingPhotos: _servicePhotos,
                    onPhotosChanged: (photos) {
                      setState(() {
                        _servicePhotos = photos;
                      });
                    },
                  ),
                  ServiceHistoryWidget(
                    serviceHistory: _serviceHistory,
                  ),
                  RecurringScheduleWidget(
                    scheduleData: _scheduleData,
                    onModifySchedule: _handleModifySchedule,
                  ),
                  SizedBox(height: 10.h), // Space for action buttons
                ],
              ),
            ),
          ),
          ActionButtonsWidget(
            appointmentStatus: _appointmentData['status'] as String,
            onMarkComplete: _handleMarkComplete,
            onReschedule: _handleReschedule,
            onCancel: _handleCancelAppointment,
          ),
        ],
      ),
    );
  }
}
