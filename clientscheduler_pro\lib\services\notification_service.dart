import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:timezone/timezone.dart' as tz;

import './supabase_service.dart';
import 'supabase_service.dart';

class NotificationService {
  static final SupabaseClient _client = SupabaseService.instance.client;
  static FlutterLocalNotificationsPlugin? _localNotifications;

  // Initialize local notifications
  static Future<void> initialize() async {
    _localNotifications = FlutterLocalNotificationsPlugin();

    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications?.initialize(initSettings);
  }

  // Create notification in database
  static Future<Map<String, dynamic>> createNotification({
    required String type,
    required String title,
    required String message,
    Map<String, dynamic>? data,
    DateTime? scheduledFor,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final notificationData = {
        'user_id': userId,
        'type': type,
        'title': title,
        'message': message,
        'data': data,
        'scheduled_for': scheduledFor?.toIso8601String(),
      };

      final response = await _client
          .from('notifications')
          .insert(notificationData)
          .select()
          .single();
      return response;
    } catch (error) {
      throw Exception('Failed to create notification: $error');
    }
  }

  // Get all notifications for current user
  static Future<List<Map<String, dynamic>>> getAllNotifications() async {
    try {
      final response = await _client
          .from('notifications')
          .select()
          .order('created_at', ascending: false);
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch notifications: $error');
    }
  }

  // Get unread notifications
  static Future<List<Map<String, dynamic>>> getUnreadNotifications() async {
    try {
      final response = await _client
          .from('notifications')
          .select()
          .eq('is_read', false)
          .order('created_at', ascending: false);
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch unread notifications: $error');
    }
  }

  // Mark notification as read
  static Future<void> markAsRead(String notificationId) async {
    try {
      await _client
          .from('notifications')
          .update({'is_read': true}).eq('id', notificationId);
    } catch (error) {
      throw Exception('Failed to mark notification as read: $error');
    }
  }

  // Mark all notifications as read
  static Future<void> markAllAsRead() async {
    try {
      await _client
          .from('notifications')
          .update({'is_read': true}).eq('is_read', false);
    } catch (error) {
      throw Exception('Failed to mark all notifications as read: $error');
    }
  }

  // Generate daily schedule notification
  static Future<void> generateDailyScheduleNotification() async {
    try {
      // Get today's appointments count
      final today = DateTime.now().toIso8601String().split('T')[0];
      final appointmentsResponse = await _client
          .from('appointments')
          .select('id')
          .eq('scheduled_date', today)
          .inFilter(
              'status', ['scheduled', 'confirmed', 'in_progress']).count();

      final count = appointmentsResponse.count ?? 0;

      if (count > 0) {
        await createNotification(
          type: 'daily_reminder',
          title: 'Daily Schedule',
          message:
              'You have $count appointment${count > 1 ? 's' : ''} scheduled for today',
          data: {'date': today, 'count': count},
          scheduledFor: DateTime.now(),
        );

        // Show local notification
        await _showLocalNotification(
          id: DateTime.now().millisecondsSinceEpoch,
          title: 'Daily Schedule',
          body:
              'You have $count appointment${count > 1 ? 's' : ''} scheduled for today',
        );
      }
    } catch (error) {
      throw Exception('Failed to generate daily schedule notification: $error');
    }
  }

  // Generate appointment reminders
  static Future<void> generateAppointmentReminders() async {
    try {
      final tomorrow = DateTime.now().add(const Duration(days: 1));
      final tomorrowStr = tomorrow.toIso8601String().split('T')[0];

      final appointments = await _client
          .from('appointments')
          .select('''
            *,
            clients(full_name, phone_number),
            service_types(name)
          ''')
          .eq('scheduled_date', tomorrowStr)
          .inFilter('status', ['scheduled', 'confirmed'])
          .order('scheduled_time');

      for (final appointment in appointments) {
        final clientName = appointment['clients']['full_name'];
        final serviceName = appointment['service_types']['name'];
        final scheduledTime = appointment['scheduled_time'] ?? 'Not set';

        await createNotification(
          type: 'appointment_reminder',
          title: 'Appointment Reminder',
          message: 'Tomorrow: $clientName - $serviceName at $scheduledTime',
          data: {
            'appointment_id': appointment['id'],
            'client_name': clientName,
            'service_name': serviceName,
            'scheduled_time': scheduledTime,
          },
          scheduledFor:
              DateTime.now().add(const Duration(hours: 18)), // 6 PM reminder
        );
      }
    } catch (error) {
      throw Exception('Failed to generate appointment reminders: $error');
    }
  }

  // Show local notification
  static Future<void> _showLocalNotification({
    required int id,
    required String title,
    required String body,
  }) async {
    if (_localNotifications == null) return;

    const androidDetails = AndroidNotificationDetails(
      'client_scheduler',
      'Client Scheduler Notifications',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const platformDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications?.show(id, title, body, platformDetails);
  }

  // Schedule daily notification (call this once during app setup)
  static Future<void> scheduleDailyNotifications() async {
    if (_localNotifications == null) return;

    // Schedule daily notification at 8:00 AM
    await _localNotifications?.zonedSchedule(
      0,
      'Daily Schedule',
      'Check your appointments for today',
      _nextInstanceOf8AM(),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'daily_schedule',
          'Daily Schedule',
        ),
        iOS: DarwinNotificationDetails(),
      ),
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }

  // Helper to get next 8:00 AM
  static tz.TZDateTime _nextInstanceOf8AM() {
    final now = tz.TZDateTime.now(tz.getLocation('UTC'));
    var scheduled =
        tz.TZDateTime(tz.getLocation('UTC'), now.year, now.month, now.day, 8);
    if (scheduled.isBefore(now)) {
      scheduled = scheduled.add(const Duration(days: 1));
    }
    return scheduled;
  }
}