-- Location: supabase/migrations/20241215073159_client_management_scheduling_system.sql
-- Schema Analysis: Fresh Supabase project with no existing schema
-- Integration Type: Complete new schema for client management and scheduling
-- Dependencies: Creating complete system from scratch

-- 1. Extensions and Types
CREATE TYPE public.user_role AS ENUM ('admin', 'manager', 'user');
CREATE TYPE public.service_status AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled');
CREATE TYPE public.appointment_status AS ENUM ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'rescheduled');
CREATE TYPE public.notification_type AS ENUM ('daily_reminder', 'appointment_reminder', 'service_due', 'client_update');

-- 2. Core Tables

-- User profiles table (intermediary for auth.users)
CREATE TABLE public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id),
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    phone_number TEXT,
    role public.user_role DEFAULT 'user'::public.user_role,
    is_active BOOLEAN DEFAULT true,
    timezone TEXT DEFAULT 'UTC',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Clients table
CREATE TABLE public.clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    email TEXT,
    phone_number TEXT,
    address TEXT,
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Service types table
CREATE TABLE public.service_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    duration_minutes INTEGER DEFAULT 60,
    price DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Recurring schedules table
CREATE TABLE public.recurring_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
    service_type_id UUID REFERENCES public.service_types(id) ON DELETE CASCADE,
    recurrence_months INTEGER NOT NULL DEFAULT 3,
    start_date DATE NOT NULL,
    next_due_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Appointments table
CREATE TABLE public.appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
    service_type_id UUID REFERENCES public.service_types(id) ON DELETE CASCADE,
    recurring_schedule_id UUID REFERENCES public.recurring_schedules(id) ON DELETE SET NULL,
    scheduled_date DATE NOT NULL,
    scheduled_time TIME,
    status public.appointment_status DEFAULT 'scheduled'::public.appointment_status,
    notes TEXT,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Service history table
CREATE TABLE public.service_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    appointment_id UUID REFERENCES public.appointments(id) ON DELETE CASCADE,
    client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
    service_type_id UUID REFERENCES public.service_types(id) ON DELETE CASCADE,
    performed_date DATE NOT NULL,
    performed_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    notes TEXT,
    photos TEXT[], -- Array of photo URLs from storage
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Notifications table
CREATE TABLE public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    type public.notification_type NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    is_read BOOLEAN DEFAULT false,
    scheduled_for TIMESTAMPTZ,
    sent_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 3. Essential Indexes
CREATE INDEX idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX idx_clients_user_id ON public.clients(user_id);
CREATE INDEX idx_clients_full_name ON public.clients(full_name);
CREATE INDEX idx_service_types_user_id ON public.service_types(user_id);
CREATE INDEX idx_recurring_schedules_user_id ON public.recurring_schedules(user_id);
CREATE INDEX idx_recurring_schedules_client_id ON public.recurring_schedules(client_id);
CREATE INDEX idx_recurring_schedules_next_due_date ON public.recurring_schedules(next_due_date);
CREATE INDEX idx_appointments_user_id ON public.appointments(user_id);
CREATE INDEX idx_appointments_client_id ON public.appointments(client_id);
CREATE INDEX idx_appointments_scheduled_date ON public.appointments(scheduled_date);
CREATE INDEX idx_appointments_status ON public.appointments(status);
CREATE INDEX idx_service_history_client_id ON public.service_history(client_id);
CREATE INDEX idx_service_history_performed_date ON public.service_history(performed_date);
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_scheduled_for ON public.notifications(scheduled_for);

-- 4. Functions (MUST BE BEFORE RLS POLICIES)

-- Function to get today's appointments
CREATE OR REPLACE FUNCTION public.get_todays_appointments(user_uuid UUID)
RETURNS TABLE(
    appointment_id UUID,
    client_name TEXT,
    service_name TEXT,
    scheduled_time TIME,
    status TEXT,
    client_phone TEXT,
    client_address TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.id,
        c.full_name,
        st.name,
        a.scheduled_time,
        a.status::TEXT,
        c.phone_number,
        c.address
    FROM public.appointments a
    JOIN public.clients c ON a.client_id = c.id
    JOIN public.service_types st ON a.service_type_id = st.id
    WHERE a.user_id = user_uuid 
    AND a.scheduled_date = CURRENT_DATE
    AND a.status IN ('scheduled', 'confirmed', 'in_progress')
    ORDER BY a.scheduled_time ASC NULLS LAST;
END;
$$;

-- Function to update recurring schedule next due date
CREATE OR REPLACE FUNCTION public.update_recurring_schedule_next_date()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        UPDATE public.recurring_schedules
        SET next_due_date = CURRENT_DATE + INTERVAL '1 month' * recurrence_months,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.recurring_schedule_id;
    END IF;
    RETURN NEW;
END;
$$;

-- Function for user profile creation trigger
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, full_name, role)
    VALUES (
        NEW.id, 
        NEW.email, 
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'role', 'user')::public.user_role
    );
    RETURN NEW;
END;
$$;

-- 5. Enable RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recurring_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- 6. RLS Policies

-- User profiles - Pattern 1: Core user table
CREATE POLICY "users_manage_own_user_profiles"
ON public.user_profiles
FOR ALL
TO authenticated
USING (id = auth.uid())
WITH CHECK (id = auth.uid());

-- Clients - Pattern 2: Simple user ownership
CREATE POLICY "users_manage_own_clients"
ON public.clients
FOR ALL
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- Service types - Pattern 2: Simple user ownership
CREATE POLICY "users_manage_own_service_types"
ON public.service_types
FOR ALL
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- Recurring schedules - Pattern 2: Simple user ownership
CREATE POLICY "users_manage_own_recurring_schedules"
ON public.recurring_schedules
FOR ALL
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- Appointments - Pattern 2: Simple user ownership
CREATE POLICY "users_manage_own_appointments"
ON public.appointments
FOR ALL
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- Service history - Pattern 2: Simple user ownership
CREATE POLICY "users_manage_own_service_history"
ON public.service_history
FOR ALL
TO authenticated
USING (client_id IN (SELECT id FROM public.clients WHERE user_id = auth.uid()));

-- Notifications - Pattern 2: Simple user ownership
CREATE POLICY "users_manage_own_notifications"
ON public.notifications
FOR ALL
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- 7. Triggers
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

CREATE TRIGGER on_appointment_completed
    AFTER UPDATE ON public.appointments
    FOR EACH ROW EXECUTE FUNCTION public.update_recurring_schedule_next_date();

-- 8. Mock Data
DO $$
DECLARE
    user1_auth_id UUID := gen_random_uuid();
    user2_auth_id UUID := gen_random_uuid();
    client1_id UUID := gen_random_uuid();
    client2_id UUID := gen_random_uuid();
    client3_id UUID := gen_random_uuid();
    service1_id UUID := gen_random_uuid();
    service2_id UUID := gen_random_uuid();
    recurring1_id UUID := gen_random_uuid();
    recurring2_id UUID := gen_random_uuid();
BEGIN
    -- Create complete auth.users records
    INSERT INTO auth.users (
        id, instance_id, aud, role, email, encrypted_password, email_confirmed_at,
        created_at, updated_at, raw_user_meta_data, raw_app_meta_data,
        is_sso_user, is_anonymous, confirmation_token, confirmation_sent_at,
        recovery_token, recovery_sent_at, email_change_token_new, email_change,
        email_change_sent_at, email_change_token_current, email_change_confirm_status,
        reauthentication_token, reauthentication_sent_at, phone, phone_change,
        phone_change_token, phone_change_sent_at
    ) VALUES
        (user1_auth_id, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('demo123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Demo User"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (user2_auth_id, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('manager123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Service Manager"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null);

    -- Create business data
    INSERT INTO public.clients (id, user_id, full_name, email, phone_number, address, notes) VALUES
        (client1_id, user1_auth_id, 'John Smith', '<EMAIL>', '******-0123', '123 Main St, Anytown, ST 12345', 'Prefers morning appointments'),
        (client2_id, user1_auth_id, 'Sarah Johnson', '<EMAIL>', '******-0124', '456 Oak Ave, Somewhere, ST 12346', 'Has two cats, allergic to strong scents'),
        (client3_id, user1_auth_id, 'Michael Brown', '<EMAIL>', '******-0125', '789 Pine Rd, Elsewhere, ST 12347', 'New client, first service');

    INSERT INTO public.service_types (id, user_id, name, description, duration_minutes, price) VALUES
        (service1_id, user1_auth_id, 'Routine Service', 'Regular maintenance service performed every 3 months', 90, 150.00),
        (service2_id, user1_auth_id, 'Deep Clean Service', 'Comprehensive deep cleaning service', 180, 300.00);

    INSERT INTO public.recurring_schedules (id, user_id, client_id, service_type_id, recurrence_months, start_date, next_due_date) VALUES
        (recurring1_id, user1_auth_id, client1_id, service1_id, 3, '2024-12-15', '2025-03-15'),
        (recurring2_id, user1_auth_id, client2_id, service1_id, 3, '2024-11-15', '2025-02-15');

    INSERT INTO public.appointments (user_id, client_id, service_type_id, recurring_schedule_id, scheduled_date, scheduled_time, status, notes) VALUES
        (user1_auth_id, client1_id, service1_id, recurring1_id, CURRENT_DATE, '09:00:00', 'scheduled', 'First appointment of the day'),
        (user1_auth_id, client2_id, service1_id, recurring2_id, CURRENT_DATE, '14:00:00', 'confirmed', 'Client confirmed via phone'),
        (user1_auth_id, client3_id, service2_id, NULL, CURRENT_DATE + INTERVAL '1 day', '10:30:00', 'scheduled', 'New client consultation');

    INSERT INTO public.service_history (client_id, service_type_id, performed_date, performed_by, notes, rating) VALUES
        (client1_id, service1_id, CURRENT_DATE - INTERVAL '3 months', user1_auth_id, 'Excellent service, client very satisfied', 5),
        (client2_id, service1_id, CURRENT_DATE - INTERVAL '3 months', user1_auth_id, 'Standard service completed successfully', 4);

    INSERT INTO public.notifications (user_id, type, title, message, scheduled_for) VALUES
        (user1_auth_id, 'daily_reminder', 'Daily Schedule', 'You have 2 appointments scheduled for today', CURRENT_TIMESTAMP),
        (user1_auth_id, 'appointment_reminder', 'Upcoming Appointment', 'Reminder: John Smith appointment at 9:00 AM', CURRENT_TIMESTAMP + INTERVAL '1 hour');

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Mock data creation failed: %', SQLERRM;
END $$;