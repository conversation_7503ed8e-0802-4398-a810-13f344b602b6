import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/greeting_header_widget.dart';
import './widgets/quick_access_button_widget.dart';
import './widgets/statistics_card_widget.dart';
import './widgets/todays_appointments_widget.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
            title: const Text('Client Scheduler Pro'),
            backgroundColor: Colors.white,
            foregroundColor: Colors.black87,
            elevation: 0,
            systemOverlayStyle: SystemUiOverlayStyle.dark,
            actions: [
              IconButton(
                  icon: const Icon(Icons.notifications_outlined),
                  onPressed: () {
                    Navigator.pushNamed(
                        context, AppRoutes.notificationsSettings);
                  }),
            ]),
        body: SingleChildScrollView(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          GreetingHeaderWidget(
            notificationCount: 0,
            onNotificationTap: () => Navigator.pushNamed(context, AppRoutes.notificationsSettings),
          ),
          SizedBox(height: 2.h),
          StatisticsCardWidget(
            title: 'Total Clients',
            subtitle: 'Active clients',
            value: '12',
            iconName: Icons.people,
            iconColor: Colors.blue,
          ),
          SizedBox(height: 2.h),
          const TodaysAppointmentsWidget(),
          SizedBox(height: 2.h),
          _buildQuickAccessSection(context),
          SizedBox(height: 2.h),
        ])),
        floatingActionButton: FloatingActionButton(
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.addEditClient);
            },
            child: const Icon(Icons.add)));
  }

  Widget _buildQuickAccessSection(BuildContext context) {
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: 4.w),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text('Quick Access',
              style: AppTheme.lightTheme.textTheme.headlineSmall
                  ?.copyWith(fontSize: 18.sp, fontWeight: FontWeight.bold)),
          SizedBox(height: 2.h),
          Row(children: [
            Expanded(
                child: QuickAccessButtonWidget(
                    title: 'Clients',
                    iconName: Icons.people,
                    backgroundColor: Colors.blue,
                    iconColor: Colors.white,
                    onTap: () =>
                        Navigator.pushNamed(context, AppRoutes.clientList))),
            SizedBox(width: 4.w),
            Expanded(
                child: QuickAccessButtonWidget(
                    title: 'Calendar',
                    iconName: Icons.calendar_today,
                    backgroundColor: Colors.green,
                    iconColor: Colors.white,
                    onTap: () =>
                        Navigator.pushNamed(context, AppRoutes.calendarView))),
          ]),
        ]));
  }
}