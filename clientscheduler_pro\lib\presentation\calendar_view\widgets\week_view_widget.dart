import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class WeekViewWidget extends StatelessWidget {
  final DateTime selectedDate;
  final Map<DateTime, List<Map<String, dynamic>>> appointments;
  final Function(Map<String, dynamic>) onAppointmentTap;
  final Function(Map<String, dynamic>) onAppointmentLongPress;

  const WeekViewWidget({
    Key? key,
    required this.selectedDate,
    required this.appointments,
    required this.onAppointmentTap,
    required this.onAppointmentLongPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final weekDates = _getWeekDates(selectedDate);

    return Container(
      height: 25.h,
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            child: Text(
              'Week View',
              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.lightTheme.colorScheme.onSurface,
              ),
            ),
          ),
          SizedBox(height: 1.h),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: 2.w),
              itemCount: weekDates.length,
              itemBuilder: (context, index) {
                final date = weekDates[index];
                return _buildDayColumn(date);
              },
            ),
          ),
        ],
      ),
    );
  }

  List<DateTime> _getWeekDates(DateTime date) {
    final startOfWeek = date.subtract(Duration(days: date.weekday % 7));
    return List.generate(7, (index) => startOfWeek.add(Duration(days: index)));
  }

  Widget _buildDayColumn(DateTime date) {
    final dayAppointments =
        appointments[DateTime(date.year, date.month, date.day)] ?? [];
    final isToday = _isSameDay(date, DateTime.now());
    final isSelected = _isSameDay(date, selectedDate);

    return Container(
      width: 20.w,
      margin: EdgeInsets.symmetric(horizontal: 1.w),
      child: Column(
        children: [
          // Day header
          Container(
            padding: EdgeInsets.symmetric(vertical: 1.h),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppTheme.lightTheme.colorScheme.primary
                      .withValues(alpha: 0.1)
                  : isToday
                      ? AppTheme.lightTheme.colorScheme.primaryContainer
                      : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Text(
                  _getDayName(date.weekday),
                  style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                    color: isToday || isSelected
                        ? AppTheme.lightTheme.colorScheme.primary
                        : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  date.day.toString(),
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    color: isToday || isSelected
                        ? AppTheme.lightTheme.colorScheme.primary
                        : AppTheme.lightTheme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 1.h),
          // Appointments
          Expanded(
            child: dayAppointments.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    itemCount:
                        dayAppointments.length > 3 ? 3 : dayAppointments.length,
                    itemBuilder: (context, index) {
                      if (index == 2 && dayAppointments.length > 3) {
                        return _buildMoreIndicator(dayAppointments.length - 2);
                      }
                      return _buildAppointmentCard(dayAppointments[index]);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentCard(Map<String, dynamic> appointment) {
    final serviceColors = {
      'Cleaning': AppTheme.lightTheme.colorScheme.primary,
      'Maintenance': AppTheme.getSuccessColor(true),
      'Consultation': AppTheme.getWarningColor(true),
      'Training': AppTheme.getErrorColor(true),
    };

    final serviceColor = serviceColors[appointment['serviceType']] ??
        AppTheme.lightTheme.colorScheme.secondary;

    return GestureDetector(
      onTap: () => onAppointmentTap(appointment),
      onLongPress: () => onAppointmentLongPress(appointment),
      child: Container(
        margin: EdgeInsets.only(bottom: 1.h),
        padding: EdgeInsets.all(2.w),
        decoration: BoxDecoration(
          color: serviceColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: serviceColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              appointment['time'] as String,
              style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                color: serviceColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 0.5.h),
            Text(
              appointment['clientName'] as String,
              style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            Text(
              appointment['serviceType'] as String,
              style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Text(
        'No appointments',
        style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildMoreIndicator(int count) {
    return Container(
      margin: EdgeInsets.only(bottom: 1.h),
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        '+$count more',
        style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  String _getDayName(int weekday) {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[weekday % 7];
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
