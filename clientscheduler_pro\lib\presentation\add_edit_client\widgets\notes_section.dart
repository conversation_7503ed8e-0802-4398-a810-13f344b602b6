import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class NotesSection extends StatelessWidget {
  final TextEditingController notesController;

  const NotesSection({
    Key? key,
    required this.notesController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notes & Special Instructions',
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.lightTheme.colorScheme.onSurface,
            ),
          ),
          Sized<PERSON><PERSON>(height: 3.h),

          TextFormField(
            controller: notesController,
            maxLines: 5,
            textInputAction: TextInputAction.newline,
            textCapitalization: TextCapitalization.sentences,
            decoration: InputDecoration(
              hintText:
                  'Add any special instructions, preferences, or important notes about this client...',
              alignLabelWithHint: true,
              contentPadding: EdgeInsets.all(4.w),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppTheme.lightTheme.colorScheme.outline
                      .withValues(alpha: 0.3),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppTheme.lightTheme.colorScheme.outline
                      .withValues(alpha: 0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppTheme.lightTheme.colorScheme.primary,
                  width: 2,
                ),
              ),
            ),
            style: AppTheme.lightTheme.textTheme.bodyMedium,
          ),

          SizedBox(height: 2.h),

          // Quick note suggestions
          Wrap(
            spacing: 2.w,
            runSpacing: 1.h,
            children: [
              _buildQuickNoteChip('Gate code required'),
              _buildQuickNoteChip('Dogs on property'),
              _buildQuickNoteChip('Key under mat'),
              _buildQuickNoteChip('Preferred morning service'),
              _buildQuickNoteChip('Allergic to chemicals'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickNoteChip(String note) {
    return Builder(
      builder: (context) => GestureDetector(
        onTap: () => _addQuickNote(context, note),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
          decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppTheme.lightTheme.colorScheme.outline
                  .withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomIconWidget(
                iconName: 'add',
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                size: 3.w,
              ),
              SizedBox(width: 1.w),
              Text(
                note,
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addQuickNote(BuildContext context, String note) {
    final currentText = notesController.text;
    final newText = currentText.isEmpty
        ? note
        : currentText.endsWith('\n') || currentText.isEmpty
            ? '$currentText$note'
            : '$currentText\n$note';

    notesController.text = newText;
    notesController.selection = TextSelection.fromPosition(
      TextPosition(offset: newText.length),
    );
  }
}
