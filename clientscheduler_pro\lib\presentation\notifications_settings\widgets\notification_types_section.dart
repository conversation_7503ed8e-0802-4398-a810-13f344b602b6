import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class NotificationTypesSection extends StatelessWidget {
  final Map<String, bool> notificationTypes;
  final Function(String, bool) onTypeToggleChanged;

  const NotificationTypesSection({
    Key? key,
    required this.notificationTypes,
    required this.onTypeToggleChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> notificationOptions = [
      {
        'key': 'dailyReminders',
        'title': 'Daily Schedule Reminders',
        'description': 'Morning notifications with today\'s client list',
        'icon': 'schedule',
      },
      {
        'key': 'overdueAlerts',
        'title': 'Overdue Appointment Alerts',
        'description': 'Notifications for missed appointments',
        'icon': 'warning',
      },
      {
        'key': 'upcomingWarnings',
        'title': 'Upcoming Appointment Warnings',
        'description': 'Alerts 1 day and 2 hours before appointments',
        'icon': 'notification_important',
      },
      {
        'key': 'completionReminders',
        'title': 'Service Completion Reminders',
        'description': 'Reminders to mark services as completed',
        'icon': 'task_alt',
      },
    ];

    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notification Types',
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.lightTheme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Choose which types of notifications you want to receive',
            style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 2.h),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: notificationOptions.length,
            separatorBuilder: (context, index) => SizedBox(height: 2.h),
            itemBuilder: (context, index) {
              final option = notificationOptions[index];
              final isEnabled = notificationTypes[option['key']] ?? false;

              return Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: isEnabled
                      ? AppTheme.lightTheme.primaryColor.withValues(alpha: 0.05)
                      : AppTheme.lightTheme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isEnabled
                        ? AppTheme.lightTheme.primaryColor
                            .withValues(alpha: 0.2)
                        : AppTheme.lightTheme.colorScheme.outline
                            .withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: isEnabled
                            ? AppTheme.lightTheme.primaryColor
                                .withValues(alpha: 0.1)
                            : AppTheme.lightTheme.colorScheme.outline
                                .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: CustomIconWidget(
                        iconName: option['icon'] as String,
                        color: isEnabled
                            ? AppTheme.lightTheme.primaryColor
                            : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            option['title'] as String,
                            style: AppTheme.lightTheme.textTheme.bodyMedium
                                ?.copyWith(
                              color: AppTheme.lightTheme.colorScheme.onSurface,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 0.5.h),
                          Text(
                            option['description'] as String,
                            style: AppTheme.lightTheme.textTheme.bodySmall
                                ?.copyWith(
                              color: AppTheme
                                  .lightTheme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Switch(
                      value: isEnabled,
                      onChanged: (value) =>
                          onTypeToggleChanged(option['key'] as String, value),
                      activeColor: AppTheme.lightTheme.primaryColor,
                      inactiveThumbColor:
                          AppTheme.lightTheme.colorScheme.outline,
                      inactiveTrackColor: AppTheme
                          .lightTheme.colorScheme.outline
                          .withValues(alpha: 0.3),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
