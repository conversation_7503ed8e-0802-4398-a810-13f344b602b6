class AppointmentModel {
  final String id;
  final String userId;
  final String clientId;
  final String serviceTypeId;
  final String? recurringScheduleId;
  final DateTime scheduledDate;
  final String? scheduledTime;
  final String status;
  final String? notes;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Related data (when joined)
  final Map<String, dynamic>? client;
  final Map<String, dynamic>? serviceType;
  final Map<String, dynamic>? recurringSchedule;

  AppointmentModel({
    required this.id,
    required this.userId,
    required this.clientId,
    required this.serviceTypeId,
    this.recurringScheduleId,
    required this.scheduledDate,
    this.scheduledTime,
    required this.status,
    this.notes,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
    this.client,
    this.serviceType,
    this.recurringSchedule,
  });

  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    return AppointmentModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      clientId: json['client_id'] as String,
      serviceTypeId: json['service_type_id'] as String,
      recurringScheduleId: json['recurring_schedule_id'] as String?,
      scheduledDate: DateTime.parse(json['scheduled_date'] as String),
      scheduledTime: json['scheduled_time'] as String?,
      status: json['status'] as String,
      notes: json['notes'] as String?,
      completedAt: json['completed_at'] != null
          ? DateTime.parse(json['completed_at'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      client: json['clients'] as Map<String, dynamic>?,
      serviceType: json['service_types'] as Map<String, dynamic>?,
      recurringSchedule: json['recurring_schedules'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'client_id': clientId,
      'service_type_id': serviceTypeId,
      'recurring_schedule_id': recurringScheduleId,
      'scheduled_date': scheduledDate.toIso8601String().split('T')[0],
      'scheduled_time': scheduledTime,
      'status': status,
      'notes': notes,
      'completed_at': completedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Helper getters for related data
  String get clientName => client?['full_name'] ?? 'Unknown Client';
  String? get clientPhone => client?['phone_number'];
  String? get clientAddress => client?['address'];
  String? get clientEmail => client?['email'];

  String get serviceName => serviceType?['name'] ?? 'Unknown Service';
  int get serviceDuration => serviceType?['duration_minutes'] ?? 60;
  String? get serviceDescription => serviceType?['description'];
  double? get servicePrice => serviceType?['price']?.toDouble();

  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  bool get isScheduled => status == 'scheduled';
  bool get isConfirmed => status == 'confirmed';
  bool get isInProgress => status == 'in_progress';

  AppointmentModel copyWith({
    String? id,
    String? userId,
    String? clientId,
    String? serviceTypeId,
    String? recurringScheduleId,
    DateTime? scheduledDate,
    String? scheduledTime,
    String? status,
    String? notes,
    DateTime? completedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? client,
    Map<String, dynamic>? serviceType,
    Map<String, dynamic>? recurringSchedule,
  }) {
    return AppointmentModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      clientId: clientId ?? this.clientId,
      serviceTypeId: serviceTypeId ?? this.serviceTypeId,
      recurringScheduleId: recurringScheduleId ?? this.recurringScheduleId,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      client: client ?? this.client,
      serviceType: serviceType ?? this.serviceType,
      recurringSchedule: recurringSchedule ?? this.recurringSchedule,
    );
  }

  @override
  String toString() {
    return 'AppointmentModel(id: $id, clientName: $clientName, serviceName: $serviceName, scheduledDate: $scheduledDate, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppointmentModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
