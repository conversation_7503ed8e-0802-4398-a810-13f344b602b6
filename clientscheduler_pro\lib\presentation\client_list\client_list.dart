import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import '../../widgets/custom_icon_widget.dart';
import './widgets/client_card_widget.dart';
import './widgets/empty_state_widget.dart';
import './widgets/filter_bottom_sheet_widget.dart';
import './widgets/search_filter_header_widget.dart';
import './widgets/section_header_widget.dart';

class ClientList extends StatefulWidget {
  const ClientList({Key? key}) : super(key: key);

  @override
  State<ClientList> createState() => _ClientListState();
}

class _ClientListState extends State<ClientList> with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  String _searchQuery = '';
  Map<String, dynamic> _activeFilters = {};
  bool _isRefreshing = false;
  int _currentTabIndex = 1; // Clients tab is active

  // Mock client data
  final List<Map<String, dynamic>> _allClients = [
    {
      "id": 1,
      "name": "<PERSON>",
      "primaryService": "House Cleaning",
      "lastServiceDate": "2025-08-10",
      "nextDueDate": "2025-11-10",
      "phone": "+****************",
      "email": "<EMAIL>",
      "address": "123 Oak Street, Downtown",
      "location": "Downtown",
      "scheduleStatus": "Active",
    },
    {
      "id": 2,
      "name": "Bob Smith",
      "primaryService": "Lawn Care",
      "lastServiceDate": "2025-07-15",
      "nextDueDate": "2025-08-10",
      "phone": "+****************",
      "email": "<EMAIL>",
      "address": "456 Pine Avenue, Suburbs",
      "location": "Suburbs",
      "scheduleStatus": "Active",
    },
    {
      "id": 3,
      "name": "Carol Davis",
      "primaryService": "HVAC Maintenance",
      "lastServiceDate": "2025-06-20",
      "nextDueDate": "2025-09-20",
      "phone": "+****************",
      "email": "<EMAIL>",
      "address": "789 Maple Drive, North Side",
      "location": "North Side",
      "scheduleStatus": "Active",
    },
    {
      "id": 4,
      "name": "David Wilson",
      "primaryService": "Plumbing",
      "lastServiceDate": "2025-08-05",
      "nextDueDate": "2025-11-05",
      "phone": "+****************",
      "email": "<EMAIL>",
      "address": "321 Elm Street, South Side",
      "location": "South Side",
      "scheduleStatus": "Paused",
    },
    {
      "id": 5,
      "name": "Emma Brown",
      "primaryService": "Pool Maintenance",
      "lastServiceDate": "2025-08-12",
      "nextDueDate": "2025-11-12",
      "phone": "+****************",
      "email": "<EMAIL>",
      "address": "654 Cedar Lane, East Side",
      "location": "East Side",
      "scheduleStatus": "Active",
    },
    {
      "id": 6,
      "name": "Frank Miller",
      "primaryService": "Electrical",
      "lastServiceDate": "2025-07-30",
      "nextDueDate": "2025-10-30",
      "phone": "+****************",
      "email": "<EMAIL>",
      "address": "987 Birch Road, West Side",
      "location": "West Side",
      "scheduleStatus": "Active",
    },
    {
      "id": 7,
      "name": "Grace Taylor",
      "primaryService": "Pest Control",
      "lastServiceDate": "2025-08-01",
      "nextDueDate": "2025-11-01",
      "phone": "+****************",
      "email": "<EMAIL>",
      "address": "147 Spruce Street, Downtown",
      "location": "Downtown",
      "scheduleStatus": "Active",
    },
    {
      "id": 8,
      "name": "Henry Anderson",
      "primaryService": "Appliance Repair",
      "lastServiceDate": "2025-07-25",
      "nextDueDate": "2025-10-25",
      "phone": "+****************",
      "email": "<EMAIL>",
      "address": "258 Willow Avenue, Suburbs",
      "location": "Suburbs",
      "scheduleStatus": "Completed",
    },
  ];

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Handle scroll events for sticky header or other scroll-based features
  }

  List<Map<String, dynamic>> get _filteredClients {
    List<Map<String, dynamic>> filtered = List.from(_allClients);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((client) {
        final name = (client["name"] as String? ?? "").toLowerCase();
        final service =
            (client["primaryService"] as String? ?? "").toLowerCase();
        final phone = (client["phone"] as String? ?? "").toLowerCase();
        final email = (client["email"] as String? ?? "").toLowerCase();
        final query = _searchQuery.toLowerCase();

        return name.contains(query) ||
            service.contains(query) ||
            phone.contains(query) ||
            email.contains(query);
      }).toList();
    }

    // Apply filters
    if (_activeFilters['serviceType'] != null &&
        _activeFilters['serviceType'] != 'All Services') {
      filtered = filtered
          .where((client) =>
              client["primaryService"] == _activeFilters['serviceType'])
          .toList();
    }

    if (_activeFilters['location'] != null &&
        _activeFilters['location'] != 'All Locations') {
      filtered = filtered
          .where((client) => client["location"] == _activeFilters['location'])
          .toList();
    }

    if (_activeFilters['scheduleStatus'] != null &&
        _activeFilters['scheduleStatus'] != 'All Statuses') {
      filtered = filtered
          .where((client) =>
              client["scheduleStatus"] == _activeFilters['scheduleStatus'])
          .toList();
    }

    if (_activeFilters['showOverdueOnly'] == true) {
      filtered = filtered.where((client) {
        final nextDueDate = client["nextDueDate"] as String?;
        if (nextDueDate == null || nextDueDate == "Not Scheduled") return false;

        try {
          final dueDate = DateTime.parse(nextDueDate);
          return dueDate.isBefore(DateTime.now());
        } catch (e) {
          return false;
        }
      }).toList();
    }

    // Sort alphabetically
    filtered.sort((a, b) =>
        (a["name"] as String? ?? "").compareTo(b["name"] as String? ?? ""));

    return filtered;
  }

  Map<String, List<Map<String, dynamic>>> get _groupedClients {
    final Map<String, List<Map<String, dynamic>>> grouped = {};

    for (final client in _filteredClients) {
      final firstLetter = (client["name"] as String? ?? "A")[0].toUpperCase();
      if (!grouped.containsKey(firstLetter)) {
        grouped[firstLetter] = [];
      }
      grouped[firstLetter]!.add(client);
    }

    return grouped;
  }

  int get _activeFilterCount {
    int count = 0;
    _activeFilters.forEach((key, value) {
      if (value != null &&
          value != false &&
          value != 'All Services' &&
          value != 'All Locations' &&
          value != 'All Statuses') {
        count++;
      }
    });
    return count;
  }

  Future<void> _refreshClients() async {
    setState(() {
      _isRefreshing = true;
    });

    // Simulate network refresh
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _isRefreshing = false;
    });

    // Show success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Client list updated'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheetWidget(
        currentFilters: _activeFilters,
        onFiltersChanged: (filters) {
          setState(() {
            _activeFilters = filters;
          });
        },
      ),
    );
  }

  void _navigateToAddClient() {
    Navigator.pushNamed(context, '/add-edit-client');
  }

  void _navigateToClientDetail(Map<String, dynamic> client) {
    // Navigate to client detail screen
    Navigator.pushNamed(context, '/client-detail', arguments: client);
  }

  void _callClient(Map<String, dynamic> client) {
    // Implement call functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Calling ${client["name"]}...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _messageClient(Map<String, dynamic> client) {
    // Implement message functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening message to ${client["name"]}...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _scheduleService(Map<String, dynamic> client) {
    Navigator.pushNamed(context, '/add-edit-client',
        arguments: {'client': client, 'mode': 'schedule'});
  }

  void _editClient(Map<String, dynamic> client) {
    Navigator.pushNamed(context, '/add-edit-client',
        arguments: {'client': client, 'mode': 'edit'});
  }

  void _viewClientHistory(Map<String, dynamic> client) {
    Navigator.pushNamed(context, '/appointment-detail', arguments: client);
  }

  void _deleteClient(Map<String, dynamic> client) {
    setState(() {
      _allClients.removeWhere((c) => c["id"] == client["id"]);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${client["name"]} deleted'),
        action: SnackBarAction(
          label: 'Undo',
          onPressed: () {
            setState(() {
              _allClients.add(client);
            });
          },
        ),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final groupedClients = _groupedClients;
    final bool isEmpty = _filteredClients.isEmpty;
    final bool isSearching = _searchQuery.isNotEmpty;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('Clients'),
        actions: [
          IconButton(
            onPressed: _navigateToAddClient,
            icon: CustomIconWidget(
              iconName: 'person_add',
              color: Theme.of(context).colorScheme.onSurface,
              size: 24,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          SearchFilterHeaderWidget(
            searchQuery: _searchQuery,
            onSearchChanged: (query) {
              setState(() {
                _searchQuery = query;
              });
            },
            onFilterTap: _showFilterBottomSheet,
            activeFilterCount: _activeFilterCount,
          ),
          Expanded(
            child: isEmpty
                ? EmptyStateWidget(
                    title: isSearching
                        ? 'No clients found'
                        : 'Add Your First Client',
                    subtitle: isSearching
                        ? 'Try adjusting your search or filters'
                        : 'Start building your client base by adding your first client',
                    buttonText: isSearching ? 'Clear Search' : 'Add Client',
                    onButtonPressed: isSearching
                        ? () {
                            setState(() {
                              _searchQuery = '';
                              _activeFilters.clear();
                            });
                          }
                        : _navigateToAddClient,
                    isSearchResult: isSearching,
                  )
                : RefreshIndicator(
                    onRefresh: _refreshClients,
                    child: ListView.builder(
                      controller: _scrollController,
                      physics: const AlwaysScrollableScrollPhysics(),
                      itemCount: _calculateItemCount(groupedClients),
                      itemBuilder: (context, index) {
                        return _buildListItem(context, groupedClients, index);
                      },
                    ),
                  ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddClient,
        child: CustomIconWidget(
          iconName: 'add',
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  int _calculateItemCount(
      Map<String, List<Map<String, dynamic>>> groupedClients) {
    int count = 0;
    groupedClients.forEach((letter, clients) {
      count += 1; // Section header
      count += clients.length; // Client cards
    });
    return count;
  }

  Widget _buildListItem(
    BuildContext context,
    Map<String, List<Map<String, dynamic>>> groupedClients,
    int index,
  ) {
    int currentIndex = 0;

    for (final entry in groupedClients.entries) {
      final letter = entry.key;
      final clients = entry.value;

      // Check if this is the section header
      if (currentIndex == index) {
        return SectionHeaderWidget(
          letter: letter,
          clientCount: clients.length,
        );
      }
      currentIndex++;

      // Check if this is one of the client cards in this section
      for (int i = 0; i < clients.length; i++) {
        if (currentIndex == index) {
          return ClientCardWidget(
            client: clients[i],
            onTap: () => _navigateToClientDetail(clients[i]),
            onCall: () => _callClient(clients[i]),
            onMessage: () => _messageClient(clients[i]),
            onSchedule: () => _scheduleService(clients[i]),
            onEdit: () => _editClient(clients[i]),
            onViewHistory: () => _viewClientHistory(clients[i]),
            onDelete: () => _deleteClient(clients[i]),
          );
        }
        currentIndex++;
      }
    }

    return const SizedBox.shrink();
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _currentTabIndex,
      onTap: (index) {
        setState(() {
          _currentTabIndex = index;
        });

        switch (index) {
          case 0:
            Navigator.pushReplacementNamed(context, '/dashboard');
            break;
          case 1:
            // Already on clients tab
            break;
          case 2:
            Navigator.pushReplacementNamed(context, '/calendar-view');
            break;
          case 3:
            Navigator.pushReplacementNamed(context, '/notifications-settings');
            break;
        }
      },
      type: BottomNavigationBarType.fixed,
      items: [
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'dashboard',
            color: _currentTabIndex == 0
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurfaceVariant,
            size: 24,
          ),
          label: 'Dashboard',
        ),
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'people',
            color: _currentTabIndex == 1
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurfaceVariant,
            size: 24,
          ),
          label: 'Clients',
        ),
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'calendar_today',
            color: _currentTabIndex == 2
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurfaceVariant,
            size: 24,
          ),
          label: 'Calendar',
        ),
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'notifications',
            color: _currentTabIndex == 3
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurfaceVariant,
            size: 24,
          ),
          label: 'Settings',
        ),
      ],
    );
  }
}
