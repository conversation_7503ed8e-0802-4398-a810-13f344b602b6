import 'package:supabase_flutter/supabase_flutter.dart';

import './supabase_service.dart';

class RecurringScheduleService {
  static final SupabaseClient _client = SupabaseService.instance.client;

  // Get all recurring schedules with client and service details
  static Future<List<Map<String, dynamic>>> getAllRecurringSchedules() async {
    try {
      final response = await _client.from('recurring_schedules').select('''
            *,
            clients(full_name, phone_number, address),
            service_types(name, duration_minutes, price)
          ''').eq('is_active', true).order('next_due_date');
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch recurring schedules: $error');
    }
  }

  // Get due schedules (clients who need service)
  static Future<List<Map<String, dynamic>>> getDueSchedules() async {
    try {
      final today = DateTime.now().toIso8601String().split('T')[0];
      final response = await _client
          .from('recurring_schedules')
          .select('''
            *,
            clients(full_name, phone_number, address, email),
            service_types(name, duration_minutes, price, description)
          ''')
          .eq('is_active', true)
          .lte('next_due_date', today)
          .order('next_due_date');
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch due schedules: $error');
    }
  }

  // Get upcoming schedules (next 30 days)
  static Future<List<Map<String, dynamic>>> getUpcomingSchedules() async {
    try {
      final today = DateTime.now();
      final nextMonth = today.add(const Duration(days: 30));

      final response = await _client
          .from('recurring_schedules')
          .select('''
            *,
            clients(full_name, phone_number),
            service_types(name)
          ''')
          .eq('is_active', true)
          .gte('next_due_date', today.toIso8601String().split('T')[0])
          .lte('next_due_date', nextMonth.toIso8601String().split('T')[0])
          .order('next_due_date');
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch upcoming schedules: $error');
    }
  }

  // Create new recurring schedule
  static Future<Map<String, dynamic>> createRecurringSchedule({
    required String clientId,
    required String serviceTypeId,
    required int recurrenceMonths,
    required DateTime startDate,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final nextDueDate = DateTime(
        startDate.year,
        startDate.month + recurrenceMonths,
        startDate.day,
      );

      final scheduleData = {
        'user_id': userId,
        'client_id': clientId,
        'service_type_id': serviceTypeId,
        'recurrence_months': recurrenceMonths,
        'start_date': startDate.toIso8601String().split('T')[0],
        'next_due_date': nextDueDate.toIso8601String().split('T')[0],
      };

      final response = await _client
          .from('recurring_schedules')
          .insert(scheduleData)
          .select('''
            *,
            clients(full_name, phone_number, address),
            service_types(name, duration_minutes, price)
          ''').single();
      return response;
    } catch (error) {
      throw Exception('Failed to create recurring schedule: $error');
    }
  }

  // Update recurring schedule
  static Future<Map<String, dynamic>> updateRecurringSchedule({
    required String scheduleId,
    int? recurrenceMonths,
    DateTime? nextDueDate,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (recurrenceMonths != null)
        updateData['recurrence_months'] = recurrenceMonths;
      if (nextDueDate != null) {
        updateData['next_due_date'] =
            nextDueDate.toIso8601String().split('T')[0];
      }

      final response = await _client
          .from('recurring_schedules')
          .update(updateData)
          .eq('id', scheduleId)
          .select('''
            *,
            clients(full_name, phone_number, address),
            service_types(name, duration_minutes, price)
          ''').single();
      return response;
    } catch (error) {
      throw Exception('Failed to update recurring schedule: $error');
    }
  }

  // Deactivate recurring schedule
  static Future<void> deactivateSchedule(String scheduleId) async {
    try {
      await _client.from('recurring_schedules').update({
        'is_active': false,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', scheduleId);
    } catch (error) {
      throw Exception('Failed to deactivate schedule: $error');
    }
  }

  // Get schedules for a specific client
  static Future<List<Map<String, dynamic>>> getClientSchedules(
      String clientId) async {
    try {
      final response = await _client
          .from('recurring_schedules')
          .select('''
            *,
            service_types(name, duration_minutes, price, description)
          ''')
          .eq('client_id', clientId)
          .eq('is_active', true)
          .order('next_due_date');
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch client schedules: $error');
    }
  }

  // Generate appointments from due schedules
  static Future<List<Map<String, dynamic>>>
      generateAppointmentsFromDueSchedules() async {
    try {
      final dueSchedules = await getDueSchedules();
      final generatedAppointments = <Map<String, dynamic>>[];

      for (final schedule in dueSchedules) {
        final appointmentData = {
          'user_id': schedule['user_id'],
          'client_id': schedule['client_id'],
          'service_type_id': schedule['service_type_id'],
          'recurring_schedule_id': schedule['id'],
          'scheduled_date': DateTime.now().toIso8601String().split('T')[0],
          'status': 'scheduled',
          'notes': 'Auto-generated from recurring schedule',
        };

        final appointment = await _client
            .from('appointments')
            .insert(appointmentData)
            .select('''
              *,
              clients(full_name, phone_number),
              service_types(name)
            ''').single();

        generatedAppointments.add(appointment);
      }

      return generatedAppointments;
    } catch (error) {
      throw Exception('Failed to generate appointments: $error');
    }
  }
}
