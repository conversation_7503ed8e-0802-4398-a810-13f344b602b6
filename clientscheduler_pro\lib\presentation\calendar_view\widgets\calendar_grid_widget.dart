import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class CalendarGridWidget extends StatelessWidget {
  final DateTime currentMonth;
  final DateTime selectedDate;
  final DateTime today;
  final Function(DateTime) onDateSelected;
  final Map<DateTime, List<Map<String, dynamic>>> appointments;

  const CalendarGridWidget({
    Key? key,
    required this.currentMonth,
    required this.selectedDate,
    required this.today,
    required this.onDateSelected,
    required this.appointments,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w),
      child: Column(
        children: [
          // Week day headers
          _buildWeekDayHeaders(),
          SizedBox(height: 1.h),
          // Calendar grid
          _buildCalendarGrid(),
        ],
      ),
    );
  }

  Widget _buildWeekDayHeaders() {
    final weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    return Row(
      children: weekDays
          .map((day) => Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 1.h),
                  child: Text(
                    day,
                    textAlign: TextAlign.center,
                    style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ))
          .toList(),
    );
  }

  Widget _buildCalendarGrid() {
    final firstDayOfMonth = DateTime(currentMonth.year, currentMonth.month, 1);
    final lastDayOfMonth =
        DateTime(currentMonth.year, currentMonth.month + 1, 0);
    final firstDayWeekday = firstDayOfMonth.weekday % 7;
    final daysInMonth = lastDayOfMonth.day;

    List<Widget> dayWidgets = [];

    // Add empty cells for days before the first day of the month
    for (int i = 0; i < firstDayWeekday; i++) {
      dayWidgets.add(Container());
    }

    // Add day cells
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(currentMonth.year, currentMonth.month, day);
      dayWidgets.add(_buildDayCell(date));
    }

    return GridView.count(
      crossAxisCount: 7,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.0,
      children: dayWidgets,
    );
  }

  Widget _buildDayCell(DateTime date) {
    final isToday = _isSameDay(date, today);
    final isSelected = _isSameDay(date, selectedDate);
    final dayAppointments =
        appointments[DateTime(date.year, date.month, date.day)] ?? [];
    final hasAppointments = dayAppointments.isNotEmpty;

    return GestureDetector(
      onTap: () => onDateSelected(date),
      child: Container(
        margin: EdgeInsets.all(0.5.w),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.lightTheme.colorScheme.primary.withValues(alpha: 0.1)
              : isToday
                  ? AppTheme.lightTheme.colorScheme.primaryContainer
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: isToday
              ? Border.all(
                  color: AppTheme.lightTheme.colorScheme.primary,
                  width: 2,
                )
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              date.day.toString(),
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: isToday || isSelected
                    ? AppTheme.lightTheme.colorScheme.primary
                    : AppTheme.lightTheme.colorScheme.onSurface,
                fontWeight:
                    isToday || isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
            if (hasAppointments) ...[
              SizedBox(height: 0.5.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: _buildAppointmentIndicators(dayAppointments),
              ),
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildAppointmentIndicators(
      List<Map<String, dynamic>> dayAppointments) {
    final serviceColors = {
      'Cleaning': AppTheme.lightTheme.colorScheme.primary,
      'Maintenance': AppTheme.getSuccessColor(true),
      'Consultation': AppTheme.getWarningColor(true),
      'Training': AppTheme.getErrorColor(true),
    };

    Set<String> uniqueServices = {};
    for (var appointment in dayAppointments) {
      uniqueServices.add(appointment['serviceType'] as String);
      if (uniqueServices.length >= 3) break; // Max 3 indicators
    }

    return uniqueServices
        .map((service) => Container(
              width: 1.5.w,
              height: 1.5.w,
              margin: EdgeInsets.symmetric(horizontal: 0.2.w),
              decoration: BoxDecoration(
                color: serviceColors[service] ??
                    AppTheme.lightTheme.colorScheme.secondary,
                shape: BoxShape.circle,
              ),
            ))
        .toList();
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
